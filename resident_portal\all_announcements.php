<?php
// Start session
session_start();

// Include necessary files
require_once '../includes/config/database.php';
require_once '../includes/functions/utility.php';

// Check if user is logged in
$is_logged_in = isset($_SESSION['resident_id']);
$resident_id = $is_logged_in ? $_SESSION['resident_id'] : 0;

// Set default barangay information
$barangay_name = "Brgy. Talisay";
$barangay_address = "Talisay Street, Barangay Talisay";
$contact_number = "(123) 456-7890";
$email = "<EMAIL>";
$facebook_url = "";
$twitter_url = "";
$instagram_url = "";

// Load system settings
try {
    $settings_query = "SELECT * FROM system_settings WHERE setting_name IN ('barangay_name', 'barangay_address', 'contact_number', 'email', 'facebook_url', 'twitter_url', 'instagram_url') LIMIT 7";
    $settings_stmt = $conn->prepare($settings_query);
    $settings_stmt->execute();

    if ($settings_stmt->rowCount() > 0) {
        while ($row = $settings_stmt->fetch()) {
            if ($row['setting_name'] == 'barangay_name') {
                $barangay_name = $row['setting_value'];
            } elseif ($row['setting_name'] == 'barangay_address') {
                $barangay_address = $row['setting_value'];
            } elseif ($row['setting_name'] == 'contact_number') {
                $contact_number = $row['setting_value'];
            } elseif ($row['setting_name'] == 'email') {
                $email = $row['setting_value'];
            } elseif ($row['setting_name'] == 'facebook_url') {
                $facebook_url = $row['setting_value'];
            } elseif ($row['setting_name'] == 'twitter_url') {
                $twitter_url = $row['setting_value'];
            } elseif ($row['setting_name'] == 'instagram_url') {
                $instagram_url = $row['setting_value'];
            }
        }
    }
} catch (PDOException $e) {
    // Use default values if there's an error
}

// Initialize variables
$announcements = [];
$error_message = "";

// Pagination
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = 9; // 9 announcements per page (3x3 grid)
$offset = ($page - 1) * $limit;
$total_pages = 1;

try {
    // Connect to database
    $conn = new PDO("mysql:host=$db_host;dbname=$db_name", $db_user, $db_pass);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Get total count for pagination
    $count_stmt = $conn->prepare("SELECT COUNT(*) as total FROM announcements WHERE status = 'Active' AND is_featured = 1");
    $count_stmt->execute();
    $total_records = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    $total_pages = ceil($total_records / $limit);

    // Get public information with pagination
    $stmt = $conn->prepare("SELECT *,
                           CASE
                              WHEN document_type = 'Ordinance' THEN '⚖️'
                              WHEN document_type = 'Executive Order' THEN '📜'
                              ELSE '📣'
                           END as doc_icon
                           FROM announcements
                           WHERE status = 'Active' AND is_featured = 1
                           ORDER BY date_posted DESC
                           LIMIT :limit OFFSET :offset");
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();
    $announcements = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $error_message = "Database error: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Public Information | <?php echo $barangay_name; ?> Resident Portal</title>

    <!-- Favicon -->
    <link rel="icon" href="<?php echo get_favicon_url($conn, '../'); ?>" type="image/png">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #4e73df;
            --primary-dark: #3a56b7;
            --primary-light: rgba(78, 115, 223, 0.1);
            --secondary-color: #1cc88a;
            --secondary-dark: #169e6c;
            --secondary-light: rgba(28, 200, 138, 0.1);
            --dark-color: #2e384d;
            --light-color: #f8f9fc;
            --gray-color: #858796;
            --danger-color: #e74a3b;
            --warning-color: #f6c23e;
            --info-color: #36b9cc;
            --card-border-radius: 1rem;
            --btn-border-radius: 0.5rem;
            --box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            --box-shadow-hover: 0 1rem 3rem rgba(0, 0, 0, 0.15);
            --transition: all 0.3s ease;
            --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            --gradient-secondary: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
            --gradient-hero: linear-gradient(135deg, rgba(46, 56, 77, 0.95), rgba(78, 115, 223, 0.9));

            /* For backward compatibility */
            --primary: #4e73df;
            --primary-light: rgba(78, 115, 223, 0.1);
            --secondary: #1cc88a;
            --success: #1cc88a;
            --info: #36b9cc;
            --warning: #f6c23e;
            --danger: #e74a3b;
            --dark: #2e384d;
            --light: #f8f9fc;
            --card-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            --bs-font-sans-serif: 'Poppins', sans-serif;
        }

        body {
            font-family: var(--bs-font-sans-serif);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            background-color: #f5f7ff;
        }

        /* Navbar styling */
        .navbar-brand {
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .navbar-brand-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-size: 1.2rem;
        }

        .nav-link {
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: var(--transition);
            display: flex;
            align-items: center;
        }

        .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .nav-link.active {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .nav-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            transition: var(--transition);
        }

        .nav-link:hover .nav-icon {
            transform: scale(1.1);
        }

        /* Page header */
        .page-header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            padding: 3rem 0;
            color: white;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
        }

        .page-header h1 {
            font-weight: 700;
            display: flex;
            align-items: center;
        }

        .page-header .icon-container {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }

        /* Announcement card */
        .announcement-card {
            border: none;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--card-shadow);
            margin-bottom: 2rem;
            background-color: white;
            height: 100%;
            display: flex;
            flex-direction: column;
            transition: var(--transition);
        }

        .announcement-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }

        /* Featured Announcement Styling */
        .featured-announcement {
            position: relative;
            border: 2px solid #ffc107 !important;
            box-shadow: 0 8px 25px rgba(255, 193, 7, 0.2) !important;
        }

        .featured-announcement:hover {
            box-shadow: 0 15px 35px rgba(255, 193, 7, 0.3) !important;
        }

        .featured-badge {
            position: absolute;
            top: -1px;
            right: -1px;
            z-index: 10;
        }

        .featured-badge .badge {
            border-radius: 0 15px 0 15px;
            padding: 0.5rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .card-body {
            flex: 1 1 auto;
            padding: 1.5rem;
        }

        .card-footer {
            padding: 1rem 1.5rem;
            background-color: rgba(0, 0, 0, 0.02);
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }

        .announcement-date {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .announcement-type {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-size: 0.8rem;
            font-weight: 500;
            margin-bottom: 1rem;
        }

        .announcement-type.general {
            background-color: rgba(67, 97, 238, 0.1);
            color: var(--primary);
        }

        .announcement-type.event {
            background-color: rgba(114, 9, 183, 0.1);
            color: var(--secondary);
        }

        .announcement-type.emergency {
            background-color: rgba(231, 29, 54, 0.1);
            color: var(--danger);
        }

        .announcement-type.meeting {
            background-color: rgba(58, 134, 255, 0.1);
            color: var(--info);
        }

        .announcement-type.community {
            background-color: rgba(255, 159, 28, 0.1);
            color: var(--warning);
        }

        /* Announcement Modal Styles */
        .announcement-meta {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            color: #6c757d;
            font-size: 0.9rem;
            gap: 1rem;
        }

        .announcement-meta-item {
            display: flex;
            align-items: center;
        }

        .announcement-meta-item i {
            margin-right: 0.5rem;
        }

        /* Pagination */
        .pagination {
            justify-content: center;
            margin-top: 2rem;
        }

        .page-link {
            color: var(--primary);
            border: none;
            margin: 0 5px;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition);
        }

        .page-link:hover {
            background-color: rgba(67, 97, 238, 0.1);
            color: var(--primary);
            transform: translateY(-3px);
        }

        .page-item.active .page-link {
            background-color: var(--primary);
            color: white;
        }

        .page-item.disabled .page-link {
            color: #6c757d;
            opacity: 0.5;
        }

        /* Button Styling */
        .btn {
            font-weight: 500;
            border-radius: 8px;
            padding: 0.5rem 1.25rem;
            transition: var(--transition);
        }

        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }

        .btn-primary:hover {
            background-color: var(--secondary);
            border-color: var(--secondary);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
        }

        /* Footer */
        footer {
            background: linear-gradient(135deg, var(--dark) 0%, #2a2a72 100%);
            color: white;
            padding: 3rem 0;
            margin-top: auto;
        }

        footer h5 {
            font-weight: 600;
            margin-bottom: 1.5rem;
            position: relative;
            padding-bottom: 0.75rem;
        }

        footer h5::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 40px;
            height: 3px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
        }

        .social-icon {
            width: 36px;
            height: 36px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition);
        }

        .social-icon:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px);
        }

        footer a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: var(--transition);
        }

        footer a:hover {
            color: white;
            text-decoration: none;
        }

        .footer-links {
            list-style: none;
            padding-left: 0;
        }

        .footer-links li {
            margin-bottom: 0.75rem;
        }

        .footer-links li a {
            display: block;
            transition: var(--transition);
        }

        .footer-links li a:hover {
            transform: translateX(5px);
        }

        /* Animation */
        .animate-fade-in-up {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }
    </style>
</head>
<body>
    <!-- Header/Navbar -->
    <header class="header-area">
        <!-- Top Header -->
        <div class="top-header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="top-header-info">
                            <ul>
                                <li><i class="fas fa-map-marker-alt"></i> <?php echo !empty($barangay_address) ? htmlspecialchars($barangay_address) : 'Barangay Hall Address'; ?></li>
                                <li><i class="fas fa-envelope"></i> <?php echo !empty($email) ? htmlspecialchars($email) : '<EMAIL>'; ?></li>
                                <li><i class="fas fa-phone"></i> <?php echo !empty($contact_number) ? htmlspecialchars($contact_number) : 'Contact Number'; ?></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="top-header-social">
                            <?php if (!empty($facebook_url)): ?>
                            <a href="<?php echo htmlspecialchars($facebook_url); ?>" target="_blank" rel="noopener noreferrer" title="Follow us on Facebook"><i class="fab fa-facebook-f"></i></a>
                            <?php endif; ?>
                            <?php if (!empty($twitter_url)): ?>
                            <a href="<?php echo htmlspecialchars($twitter_url); ?>" target="_blank" rel="noopener noreferrer" title="Follow us on Twitter"><i class="fab fa-twitter"></i></a>
                            <?php endif; ?>
                            <?php if (!empty($instagram_url)): ?>
                            <a href="<?php echo htmlspecialchars($instagram_url); ?>" target="_blank" rel="noopener noreferrer" title="Follow us on Instagram"><i class="fab fa-instagram"></i></a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Navbar -->
        <div class="navbar-container">
            <nav class="navbar navbar-expand-lg">
                <div class="container">
                    <a class="navbar-brand" href="index.php">
                        <div class="navbar-brand-wrapper">
                            <div class="navbar-brand-icon">
                                <i class="fas fa-landmark"></i>
                            </div>
                            <div class="navbar-brand-text">
                                <span class="navbar-brand-title"><?php echo $barangay_name; ?></span>
                                <span class="navbar-brand-subtitle">Brgy. Talisay Barangay Management Systems Resident Portal</span>
                            </div>
                        </div>
                    </a>
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                        <span class="navbar-toggler-icon"><i class="fas fa-bars"></i></span>
                    </button>
                    <div class="collapse navbar-collapse" id="navbarNav">
                        <ul class="navbar-nav ms-auto">
                            <li class="nav-item">
                                <a class="nav-link active" href="index.php">
                                    <div class="nav-icon">
                                        <i class="fas fa-home"></i>
                                    </div>
                                    Home
                                </a>
                            </li>
                            <?php if ($is_logged_in): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="dashboard.php">
                                    <div class="nav-icon">
                                        <i class="fas fa-tachometer-alt"></i>
                                    </div>
                                    Dashboard
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="request_document.php">
                                    <div class="nav-icon">
                                        <i class="fas fa-file-alt"></i>
                                    </div>
                                    Request Document
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="my_requests.php">
                                    <div class="nav-icon">
                                        <i class="fas fa-clipboard-list"></i>
                                    </div>
                                    My Requests
                                </a>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <div class="nav-icon">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    Profile
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                    <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-circle me-2"></i> My Profile</a></li>
                                    <li><a class="dropdown-item" href="profile.php#password-tab"><i class="fas fa-key me-2"></i> Change Password</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                                </ul>
                            </li>
                            <?php else: ?>
                            <li class="nav-item">
                                <a class="nav-link" href="login.php">
                                    <div class="nav-icon">
                                        <i class="fas fa-sign-in-alt"></i>
                                    </div>
                                    Login
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="register.php">
                                    <div class="nav-icon">
                                        <i class="fas fa-user-plus"></i>
                                    </div>
                                    Register
                                </a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <style>
        /* Top Header */
        .top-header {
            background-color: var(--dark-color);
            padding: 0.5rem 0;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.85rem;
        }

        .top-header-info ul {
            display: flex;
            flex-wrap: wrap;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .top-header-info li {
            margin-right: 1.5rem;
            display: flex;
            align-items: center;
        }

        .top-header-info li i {
            margin-right: 0.5rem;
            color: var(--secondary-color);
        }

        /* Navbar Styling */
        .navbar-container {
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
            width: 100%;
            background-color: white;
        }

        .navbar {
            padding: 0.75rem 0;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
        }

        .navbar-brand-wrapper {
            display: flex;
            align-items: center;
        }

        .navbar-brand-icon {
            background: var(--gradient-primary);
            color: white;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(78, 115, 223, 0.3);
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }

        .navbar-brand-text {
            display: flex;
            flex-direction: column;
        }

        .navbar-brand-title {
            font-weight: 700;
            font-size: 1.25rem;
            color: var(--dark-color);
            line-height: 1.2;
        }

        .navbar-brand-subtitle {
            font-size: 0.75rem;
            color: var(--gray-color);
            font-weight: 500;
        }

        .nav-link {
            color: var(--dark-color) !important;
            font-weight: 600;
            display: flex;
            align-items: center;
            padding: 0.75rem 1.25rem;
            margin: 0 0.25rem;
            line-height: 1;
        }

        .nav-link:hover, .nav-link.active {
            background-color: var(--primary-light);
            color: var(--primary-color) !important;
        }

        .nav-icon {
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-radius: 10px;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            transition: all 0.3s ease;
        }

        .nav-link:hover .nav-icon {
            background: var(--gradient-primary);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(78, 115, 223, 0.2);
        }

        .nav-link.active .nav-icon {
            background: var(--gradient-primary);
            color: white;
        }
    </style>

    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1>
                        <div class="icon-container">
                            <i class="fas fa-bullhorn"></i>
                        </div>
                        📣 Public Information
                    </h1>
                    <p class="lead mb-0">Stay updated with the latest announcements, ordinances, executive orders, and important information from your barangay</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <a href="index.php" class="btn btn-light">
                        <i class="fas fa-arrow-left me-2"></i> Back to Homepage
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container py-4">
        <?php if ($error_message): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <?php if (empty($announcements)): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> No announcements available at this time.
            </div>
        <?php else: ?>
            <div class="row">
                <?php
                $delay = 0.1;
                foreach ($announcements as $announcement):
                    // Define announcement type classes
                    $type_classes = [
                        'General' => 'general',
                        'Event' => 'event',
                        'Meeting' => 'meeting',
                        'Emergency' => 'emergency',
                        'Community' => 'community'
                    ];
                    $type_class = isset($type_classes[$announcement['announcement_type']]) ? $type_classes[$announcement['announcement_type']] : 'general';
                ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="announcement-card animate-fade-in-up<?php echo $announcement['is_featured'] ? ' featured-announcement' : ''; ?>" style="animation-delay: <?php echo $delay; ?>s;">
                        <?php if ($announcement['is_featured']): ?>
                        <div class="featured-badge">
                            <span class="badge bg-warning text-dark">⭐ Featured</span>
                        </div>
                        <?php endif; ?>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <span class="badge bg-<?php
                                    echo ($announcement['document_type'] == 'Ordinance') ? 'success' :
                                         (($announcement['document_type'] == 'Executive Order') ? 'warning' : 'info');
                                ?>">
                                    <?php echo $announcement['doc_icon'] . ' ' . htmlspecialchars($announcement['document_type'] ?? 'Announcement'); ?>
                                </span>
                                <?php if (!empty($announcement['document_number'])): ?>
                                    <small class="text-muted">No. <?php echo htmlspecialchars($announcement['document_number']); ?></small>
                                <?php endif; ?>
                            </div>
                            <?php if (!empty($announcement['announcement_type'])): ?>
                            <div class="announcement-type <?php echo $type_class; ?>">
                                <?php echo htmlspecialchars($announcement['announcement_type']); ?>
                            </div>
                            <?php endif; ?>
                            <h5 class="card-title"><?php echo htmlspecialchars($announcement['title']); ?></h5>
                            <p class="announcement-date">
                                <i class="far fa-calendar-alt me-1"></i>
                                <?php echo date('F d, Y', strtotime($announcement['date_posted'])); ?>
                            </p>
                            <p class="card-text"><?php echo nl2br(htmlspecialchars(substr($announcement['content'], 0, 150) . (strlen($announcement['content']) > 150 ? '...' : ''))); ?></p>
                        </div>
                        <div class="card-footer">
                            <button type="button" class="btn btn-sm btn-outline-primary view-announcement-btn"
                                    data-bs-toggle="modal"
                                    data-bs-target="#viewAnnouncementModal"
                                    data-id="<?php echo $announcement['announcement_id']; ?>"
                                    data-title="<?php echo htmlspecialchars($announcement['title']); ?>"
                                    data-type="<?php echo htmlspecialchars($announcement['announcement_type'] ?? 'General'); ?>"
                                    data-date="<?php echo date('F d, Y', strtotime($announcement['date_posted'])); ?>"
                                    data-start-date="<?php echo !empty($announcement['start_date']) ? date('F d, Y', strtotime($announcement['start_date'])) : ''; ?>"
                                    data-end-date="<?php echo !empty($announcement['end_date']) ? date('F d, Y', strtotime($announcement['end_date'])) : ''; ?>"
                                    data-venue="<?php echo htmlspecialchars($announcement['venue'] ?? ''); ?>"
                                    data-content="<?php echo htmlspecialchars($announcement['content']); ?>"
                                    data-audience="<?php echo htmlspecialchars($announcement['target_audience'] ?? ''); ?>"
                                    data-image="<?php echo htmlspecialchars($announcement['image'] ?? ''); ?>">
                                <i class="fas fa-arrow-right me-1"></i> Read More
                            </button>
                        </div>
                    </div>
                </div>
                <?php
                    $delay += 0.1;
                    if ($delay > 0.9) $delay = 0.1; // Reset delay after 9 items
                endforeach;
                ?>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
            <nav aria-label="Announcements pagination">
                <ul class="pagination">
                    <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                        <a class="page-link" href="<?php echo ($page <= 1) ? '#' : '?page='.($page-1); ?>" aria-label="Previous">
                            <span aria-hidden="true"><i class="fas fa-chevron-left"></i></span>
                        </a>
                    </li>

                    <?php for($i = 1; $i <= $total_pages; $i++): ?>
                    <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                    </li>
                    <?php endfor; ?>

                    <li class="page-item <?php echo ($page >= $total_pages) ? 'disabled' : ''; ?>">
                        <a class="page-link" href="<?php echo ($page >= $total_pages) ? '#' : '?page='.($page+1); ?>" aria-label="Next">
                            <span aria-hidden="true"><i class="fas fa-chevron-right"></i></span>
                        </a>
                    </li>
                </ul>
            </nav>
            <?php endif; ?>
        <?php endif; ?>
    </div>

    <?php include 'includes/footer.php'; ?>

    <!-- View Announcement Modal -->
    <div class="modal fade" id="viewAnnouncementModal" tabindex="-1" aria-labelledby="viewAnnouncementModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewAnnouncementModalLabel">
                        <i class="fas fa-bullhorn me-2"></i>
                        Announcement Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="announcement-header mb-4">
                        <div class="announcement-type mb-2" id="announcementType"></div>
                        <h3 class="mb-3" id="announcementTitle"></h3>
                        <div class="announcement-meta">
                            <div class="announcement-meta-item">
                                <i class="far fa-calendar-alt"></i>
                                <span id="announcementDate"></span>
                            </div>
                            <div class="announcement-meta-item" id="startDateContainer" style="display: none;">
                                <i class="fas fa-calendar-day"></i>
                                <span id="announcementStartDate"></span>
                            </div>
                            <div class="announcement-meta-item" id="endDateContainer" style="display: none;">
                                <i class="fas fa-calendar-check"></i>
                                <span id="announcementEndDate"></span>
                            </div>
                        </div>
                    </div>

                    <div class="announcement-venue mb-4" id="venueContainer" style="display: none;">
                        <h5><i class="fas fa-map-marker-alt me-2"></i> Venue</h5>
                        <p id="announcementVenue"></p>
                    </div>

                    <div class="announcement-content mb-4">
                        <h5><i class="fas fa-align-left me-2"></i> Details</h5>
                        <div class="mt-3" id="announcementContent"></div>
                    </div>

                    <div class="announcement-audience mb-4" id="audienceContainer" style="display: none;">
                        <h5><i class="fas fa-users me-2"></i> Target Audience</h5>
                        <p id="announcementAudience"></p>
                    </div>

                    <div class="announcement-image mb-4" id="imageContainer" style="display: none;">
                        <h5><i class="fas fa-image me-2"></i> Attached Image</h5>
                        <div class="mt-3">
                            <img id="announcementImage" class="img-fluid rounded" alt="Announcement Image">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i> Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Animate elements when they come into view
            function animateOnScroll() {
                $('.animate-fade-in-up').each(function() {
                    var elementPosition = $(this).offset().top;
                    var topOfWindow = $(window).scrollTop();
                    var windowHeight = $(window).height();

                    if (elementPosition < topOfWindow + windowHeight - 100) {
                        $(this).css('opacity', '1');
                        $(this).css('transform', 'translateY(0)');
                    }
                });
            }

            // Run animation on page load
            animateOnScroll();

            // Run animation on scroll
            $(window).on('scroll', function() {
                animateOnScroll();
            });

            // Handle announcement modal
            $('#viewAnnouncementModal').on('show.bs.modal', function (event) {
                const button = $(event.relatedTarget);

                // Get data attributes
                const id = button.data('id');
                const title = button.data('title');
                const type = button.data('type');
                const date = button.data('date');
                const startDate = button.data('start-date');
                const endDate = button.data('end-date');
                const venue = button.data('venue');
                const content = button.data('content');
                const audience = button.data('audience');
                const image = button.data('image');

                // Set modal content
                $('#announcementTitle').text(title);

                // Set announcement type with appropriate class
                const typeElement = $('#announcementType');
                typeElement.text(type);
                typeElement.removeClass('general event emergency meeting community');

                // Add the appropriate class based on type
                switch(type.toLowerCase()) {
                    case 'event':
                        typeElement.addClass('event');
                        break;
                    case 'emergency':
                        typeElement.addClass('emergency');
                        break;
                    case 'meeting':
                        typeElement.addClass('meeting');
                        break;
                    case 'community':
                        typeElement.addClass('community');
                        break;
                    default:
                        typeElement.addClass('general');
                }

                // Set dates
                $('#announcementDate').text('Posted: ' + date);

                // Handle optional fields
                if (startDate) {
                    $('#announcementStartDate').text('Starts: ' + startDate);
                    $('#startDateContainer').show();
                } else {
                    $('#startDateContainer').hide();
                }

                if (endDate) {
                    $('#announcementEndDate').text('Ends: ' + endDate);
                    $('#endDateContainer').show();
                } else {
                    $('#endDateContainer').hide();
                }

                if (venue) {
                    $('#announcementVenue').text(venue);
                    $('#venueContainer').show();
                } else {
                    $('#venueContainer').hide();
                }

                // Set content with line breaks
                $('#announcementContent').html(content.replace(/\n/g, '<br>'));

                if (audience) {
                    $('#announcementAudience').text(audience);
                    $('#audienceContainer').show();
                } else {
                    $('#audienceContainer').hide();
                }

                if (image) {
                    $('#announcementImage').attr('src', '../uploads/announcements/' + image);
                    $('#imageContainer').show();
                } else {
                    $('#imageContainer').hide();
                }
            });
        });
    </script>
</body>
</html>
