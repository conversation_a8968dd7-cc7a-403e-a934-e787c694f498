<?php
// Initialize session with more explicit settings
session_start([
    'cookie_lifetime' => 86400,
    'cookie_httponly' => true,
    'cookie_path' => '/',
]);

// Generate a persistent token that won't be changed between page loads
if (!isset($_SESSION['persistent_csrf_token'])) {
    $_SESSION['persistent_csrf_token'] = bin2hex(random_bytes(32));
}

// Redirect to dashboard if already logged in
if (isset($_SESSION['resident_id'])) {
    header("Location: dashboard.php");
    exit;
}

// Use document root to get absolute paths
$root_path = $_SERVER['DOCUMENT_ROOT'] . '/barangay';

// Include database and utility functions
require_once $root_path . '/includes/config/database.php';
require_once $root_path . '/includes/functions/utility.php';

// Ensure database connection is available
if (!isset($conn) || $conn === null) {
    // Fallback database connection
    try {
        $conn = new PDO("mysql:host=localhost;dbname=barangay_management_system", "root", "");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $conn->exec("set names utf8");
    } catch(PDOException $e) {
        error_log("Register.php - Fallback database connection failed: " . $e->getMessage());
    }
}

// Get system settings
$barangay_name = "Barangay";
$barangay_address = "";
$contact_number = "";
$email = "";
$office_hours_weekday = "Monday to Friday: 8:00 AM - 5:00 PM";
$office_hours_weekend = "Saturday: 8:00 AM - 12:00 PM";
$barangay_description = "The Barangay Resident Portal provides online services to residents, making it easier to request documents and access barangay services from the comfort of your home.";
$facebook_url = "";
$twitter_url = "";
$instagram_url = "";
try {
    // Test database connection
    if (!isset($conn)) {
        error_log("Register.php - Database connection not established");
        throw new Exception("Database connection not available");
    }

    $query = "SELECT * FROM system_settings WHERE setting_name IN ('barangay_name', 'barangay_address', 'contact_number', 'email', 'office_hours_weekday', 'office_hours_weekend', 'barangay_description', 'facebook_url', 'twitter_url', 'instagram_url') LIMIT 10";
    $stmt = $conn->prepare($query);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        while ($row = $stmt->fetch()) {
            if ($row['setting_name'] == 'barangay_name') {
                $barangay_name = $row['setting_value'];
            } elseif ($row['setting_name'] == 'barangay_address') {
                $barangay_address = $row['setting_value'];
            } elseif ($row['setting_name'] == 'contact_number') {
                $contact_number = $row['setting_value'];
            } elseif ($row['setting_name'] == 'email') {
                $email = $row['setting_value'];
            } elseif ($row['setting_name'] == 'office_hours_weekday') {
                $office_hours_weekday = $row['setting_value'];
            } elseif ($row['setting_name'] == 'office_hours_weekend') {
                $office_hours_weekend = $row['setting_value'];
            } elseif ($row['setting_name'] == 'barangay_description') {
                $barangay_description = $row['setting_value'];
            } elseif ($row['setting_name'] == 'facebook_url') {
                $facebook_url = $row['setting_value'];
            } elseif ($row['setting_name'] == 'twitter_url') {
                $twitter_url = $row['setting_value'];
            } elseif ($row['setting_name'] == 'instagram_url') {
                $instagram_url = $row['setting_value'];
            }
        }
    }
} catch (PDOException $e) {
    // Default values if there's an error
    error_log("Register.php - Database error loading settings: " . $e->getMessage());
}

// Initialize variables
$first_name = $middle_name = $last_name = $gender = "";
$birthdate = $contact_number = $email = $address = "";
$civil_status = $occupation = $voter_status = "";
$place_of_birth = $nationality = "Filipino";
$religion = $educational_attainment = "";
$monthly_income = "";
$is_pwd = $is_senior = $is_solo_parent = $is_indigenous = 0;
$pwd_type = $pwd_id = $senior_id = "";
$username = $password = $confirm_password = "";
$success_message = $error_message = "";

// Use the persistent token instead of generating a new one each time
$csrf_token = $_SESSION['persistent_csrf_token'];

// Process registration form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['persistent_csrf_token']) {
        $error_message = "Invalid form submission. Please try again.";
    } else {
        // Get form data - Basic information
        $first_name = trim($_POST['first_name']);
        $middle_name = trim($_POST['middle_name'] ?? '');
        $last_name = trim($_POST['last_name']);
        $gender = $_POST['gender'];
        $birthdate = $_POST['birthdate'];
        $civil_status = $_POST['civil_status'];
        $address = trim($_POST['address']);
        $contact_number = trim($_POST['contact_number']);
        $email = trim($_POST['email']);

        // Additional information
        $occupation = trim($_POST['occupation'] ?? '');
        $voter_status = $_POST['voter_status'] ?? 'Not Registered';
        $place_of_birth = trim($_POST['place_of_birth'] ?? '');
        $nationality = trim($_POST['nationality'] ?? 'Filipino');
        $religion = trim($_POST['religion'] ?? '');
        $educational_attainment = trim($_POST['educational_attainment'] ?? '');
        $monthly_income = trim($_POST['monthly_income'] ?? '');

        // Special categories
        $is_pwd = isset($_POST['is_pwd']) ? 1 : 0;
        $is_senior = isset($_POST['is_senior']) ? 1 : 0;
        $is_solo_parent = isset($_POST['is_solo_parent']) ? 1 : 0;
        $is_indigenous = isset($_POST['is_indigenous']) ? 1 : 0;
        $pwd_type = $is_pwd ? trim($_POST['pwd_type'] ?? '') : '';
        $pwd_id = $is_pwd ? trim($_POST['pwd_id'] ?? '') : '';
        $senior_id = $is_senior ? trim($_POST['senior_id'] ?? '') : '';

        // Account information
        $username = trim($_POST['username']);
        $password = $_POST['password'];
        $confirm_password = $_POST['confirm_password'];

        // Basic validation
        $errors = [];

        if (empty($first_name)) $errors[] = "First name is required.";
        if (empty($last_name)) $errors[] = "Last name is required.";
        if (empty($gender)) $errors[] = "Gender is required.";
        if (empty($birthdate)) $errors[] = "Birthdate is required.";
        if (empty($civil_status)) $errors[] = "Civil status is required.";
        if (empty($contact_number)) $errors[] = "Contact number is required.";
        if (empty($email)) $errors[] = "Email is required.";
        if (empty($address)) $errors[] = "Address is required.";
        if (empty($username)) $errors[] = "Username is required.";
        if (empty($password)) $errors[] = "Password is required.";
        if (empty($confirm_password)) $errors[] = "Confirm password is required.";

        // Validate email format
        if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = "Invalid email format.";
        }

        // Validate password
        if (!empty($password)) {
            if (strlen($password) < 8) {
                $errors[] = "Password must be at least 8 characters long.";
            }

            if ($password !== $confirm_password) {
                $errors[] = "Passwords do not match.";
            }
        }

        // Check if username and email already exist
        if (empty($errors)) {
            try {
                // Check username
                $query = "SELECT COUNT(*) FROM resident_accounts WHERE username = :username";
                $stmt = $conn->prepare($query);
                $stmt->bindParam(':username', $username);
                $stmt->execute();

                if ($stmt->fetchColumn() > 0) {
                    $errors[] = "Username is already taken.";
                }

                // Check email
                $query = "SELECT COUNT(*) FROM residents WHERE email = :email";
                $stmt = $conn->prepare($query);
                $stmt->bindParam(':email', $email);
                $stmt->execute();

                if ($stmt->fetchColumn() > 0) {
                    $errors[] = "Email is already registered.";
                }
            } catch (PDOException $e) {
                $errors[] = "Database error. Please try again later: " . $e->getMessage();
            }
        }

        // If no errors, proceed with registration
        if (empty($errors)) {
            try {
                // Start transaction
                $conn->beginTransaction();

                // Insert into residents table
                $query = "INSERT INTO residents (
                    first_name, middle_name, last_name, birthdate, gender, civil_status,
                    address, contact_number, email, occupation, voter_status,
                    date_registered, status, place_of_birth, nationality, religion,
                    educational_attainment, monthly_income, is_pwd, is_senior,
                    is_solo_parent, is_indigenous, pwd_type, pwd_id, senior_id
                ) VALUES (
                    :first_name, :middle_name, :last_name, :birthdate, :gender, :civil_status,
                    :address, :contact_number, :email, :occupation, :voter_status,
                    NOW(), 'active', :place_of_birth, :nationality, :religion,
                    :educational_attainment, :monthly_income, :is_pwd, :is_senior,
                    :is_solo_parent, :is_indigenous, :pwd_type, :pwd_id, :senior_id
                )";
                $stmt = $conn->prepare($query);

                // Bind parameters
                $stmt->bindParam(':first_name', $first_name);
                $stmt->bindParam(':middle_name', $middle_name);
                $stmt->bindParam(':last_name', $last_name);
                $stmt->bindParam(':birthdate', $birthdate);
                $stmt->bindParam(':gender', $gender);
                $stmt->bindParam(':civil_status', $civil_status);
                $stmt->bindParam(':address', $address);
                $stmt->bindParam(':contact_number', $contact_number);
                $stmt->bindParam(':email', $email);
                $stmt->bindParam(':occupation', $occupation);
                $stmt->bindParam(':voter_status', $voter_status);
                $stmt->bindParam(':place_of_birth', $place_of_birth);
                $stmt->bindParam(':nationality', $nationality);
                $stmt->bindParam(':religion', $religion);
                $stmt->bindParam(':educational_attainment', $educational_attainment);
                $stmt->bindParam(':monthly_income', $monthly_income);
                $stmt->bindParam(':is_pwd', $is_pwd, PDO::PARAM_INT);
                $stmt->bindParam(':is_senior', $is_senior, PDO::PARAM_INT);
                $stmt->bindParam(':is_solo_parent', $is_solo_parent, PDO::PARAM_INT);
                $stmt->bindParam(':is_indigenous', $is_indigenous, PDO::PARAM_INT);
                $stmt->bindParam(':pwd_type', $pwd_type);
                $stmt->bindParam(':pwd_id', $pwd_id);
                $stmt->bindParam(':senior_id', $senior_id);

                // Execute the query
                $stmt->execute();

                // Get the last inserted ID
                $resident_id = $conn->lastInsertId();

                // If resident ID is not available, roll back and show error
                if (!$resident_id) {
                    $conn->rollBack();
                    $errors[] = "Failed to create resident account. Please try again later.";
                } else {
                    // Hash the password
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);

                    // Generate verification token
                    $verification_token = bin2hex(random_bytes(32));

                    // Insert into resident_accounts table
                    $query = "INSERT INTO resident_accounts (
                        resident_id, username, password,
                        is_verified, verification_token,
                        created_at, updated_at
                    ) VALUES (
                        :resident_id, :username, :password,
                        :is_verified, :verification_token,
                        NOW(), NOW()
                    )";
                    $stmt = $conn->prepare($query);

                    // Set initial verification status
                    $is_verified = 0; // Default to unverified

                    // Bind parameters
                    $stmt->bindParam(':resident_id', $resident_id);
                    $stmt->bindParam(':username', $username);
                    $stmt->bindParam(':password', $hashed_password);
                    $stmt->bindParam(':is_verified', $is_verified, PDO::PARAM_INT);
                    $stmt->bindParam(':verification_token', $verification_token);

                    // Execute the query
                    $stmt->execute();

                    // Send verification email
                    $verification_url = "http://" . $_SERVER['HTTP_HOST'] . "/barangay/resident_portal/verify.php?token=" . $verification_token;

                    // Include email helper
                    require_once $root_path . '/includes/functions/email_helper.php';

                    // Create email content
                    $subject = "Verify Your Account - " . $barangay_name . " Resident Portal";
                    $message = "
                        <html>
                        <head>
                            <style>
                                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                                .header { background-color: #4e73df; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }
                                .content { background-color: #f8f9fc; padding: 20px; border-radius: 0 0 5px 5px; }
                                .button { display: inline-block; background-color: #4e73df; color: white; text-decoration: none; padding: 10px 20px; border-radius: 5px; margin: 20px 0; }
                                .footer { margin-top: 20px; font-size: 12px; color: #666; text-align: center; }
                            </style>
                        </head>
                        <body>
                            <div class='container'>
                                <div class='header'>
                                    <h2>Welcome to " . $barangay_name . " Resident Portal</h2>
                                </div>
                                <div class='content'>
                                    <p>Hello " . $first_name . " " . $last_name . ",</p>
                                    <p>Thank you for registering with the " . $barangay_name . " Resident Portal. To complete your registration, please verify your email address by clicking the button below:</p>
                                    <p style='text-align: center;'>
                                        <a href='" . $verification_url . "' class='button'>Verify My Email</a>
                                    </p>
                                    <p>If the button above doesn't work, you can also copy and paste the following link into your browser:</p>
                                    <p>" . $verification_url . "</p>
                                    <p>This link will expire in 24 hours.</p>
                                    <p>If you did not create an account, please ignore this email.</p>
                                </div>
                                <div class='footer'>
                                    <p>This is an automated message from the " . $barangay_name . " Resident Portal. Please do not reply to this email.</p>
                                </div>
                            </div>
                        </body>
                        </html>
                    ";

                    // Send the verification email
                    $email_result = send_email($email, $subject, $message);
                    $mail_sent = ($email_result['status'] === 'success');

                    // Try to send notification to admin users about new registration
                    try {
                        // Get admin users for notification
                        $notif_query = "SELECT user_id, email FROM users WHERE role = 'admin' OR role = 'Admin'";
                        $notif_stmt = $conn->prepare($notif_query);
                        $notif_stmt->execute();
                        $admin_users = $notif_stmt->fetchAll(PDO::FETCH_ASSOC);

                        // Get columns in notifications table
                        $notification_stmt = $conn->prepare("SHOW COLUMNS FROM notifications");
                        $notification_stmt->execute();
                        $notification_columns = $notification_stmt->fetchAll(PDO::FETCH_COLUMN, 0);

                        // Get columns in users table
                        $users_stmt = $conn->prepare("SHOW COLUMNS FROM users");
                        $users_stmt->execute();
                        $users_columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 0);

                        // Build a dynamic query based on available columns
                        $has_module_column = in_array('module', $notification_columns);
                        $has_url_column = in_array('url', $notification_columns);

                        $notif_query = "INSERT INTO notifications (
                            user_id, message, is_read, created_at" .
                            ($has_module_column ? ", module" : "") .
                            ($has_url_column ? ", url" : "") .
                        ") VALUES (
                            :user_id, :message, 0, NOW()" .
                            ($has_module_column ? ", :module" : "") .
                            ($has_url_column ? ", :url" : "") .
                        ")";

                        // For each admin user, create notification
                        foreach ($admin_users as $admin) {
                            $notif_stmt = $conn->prepare($notif_query);
                            $notif_message = "New resident registration: {$first_name} {$last_name} ({$email})";

                            // Always present parameters
                            $notif_stmt->bindParam(':user_id', $admin['user_id']);
                            $notif_stmt->bindParam(':message', $notif_message);

                            // Optional parameters based on columns
                            if ($has_module_column) {
                                $module = "residents";
                                $notif_stmt->bindParam(':module', $module);
                            }

                            if ($has_url_column) {
                                $url = "/barangay/modules/residents/view_resident.php?id=" . $resident_id;
                                $notif_stmt->bindParam(':url', $url);
                            }

                            $notif_stmt->execute();
                        }
                    } catch (PDOException $e) {
                        // Just log the error, don't stop registration process
                        error_log("Error creating notification: " . $e->getMessage());
                    }

                    // Commit transaction
                    $conn->commit();

                    // Set success message based on email status
                    if ($mail_sent) {
                        $success_message = "success_with_email";
                        $success_email = $email; // Store email for display
                    } else {
                        // Email failed but registration was successful
                        $success_message = "success_without_email";

                        // Log the email sending failure
                        error_log("Failed to send verification email to " . $email . ". Error: " . ($email_result['message'] ?? 'Unknown error'));
                    }

                    // Clear form data
                    $first_name = $middle_name = $last_name = $gender = "";
                    $birthdate = $contact_number = $email = $address = "";
                    $civil_status = $occupation = $voter_status = "";
                    $place_of_birth = $nationality = "Filipino";
                    $religion = $educational_attainment = "";
                    $monthly_income = "";
                    $is_pwd = $is_senior = $is_solo_parent = $is_indigenous = 0;
                    $pwd_type = $pwd_id = $senior_id = "";
                    $username = $password = $confirm_password = "";
                }
            } catch (PDOException $e) {
                // Rollback the transaction
                $conn->rollBack();
                $errors[] = "Registration failed. Please try again later.";
                error_log("Registration error: " . $e->getMessage());
            }
        }

        // If there are errors, combine them into the error message
        if (!empty($errors)) {
            $error_message = implode("<br>", $errors);
        }
    }
}

// Page title
$page_title = $barangay_name . " Resident Portal - Registration";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        :root {
            --primary-color: #4e73df;
            --primary-dark: #3a56b7;
            --primary-light: rgba(78, 115, 223, 0.1);
            --secondary-color: #1cc88a;
            --secondary-dark: #169e6c;
            --secondary-light: rgba(28, 200, 138, 0.1);
            --dark-color: #2e384d;
            --light-color: #f8f9fc;
            --gray-color: #858796;
            --danger-color: #e74a3b;
            --warning-color: #f6c23e;
            --info-color: #36b9cc;
            --card-border-radius: 1rem;
            --btn-border-radius: 0.5rem;
            --box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            --box-shadow-hover: 0 1rem 3rem rgba(0, 0, 0, 0.15);
            --transition: all 0.3s ease;
            --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            --gradient-secondary: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
            --gradient-hero: linear-gradient(135deg, rgba(46, 56, 77, 0.95), rgba(78, 115, 223, 0.9));

            /* For backward compatibility */
            --primary: #4e73df;
            --primary-light: rgba(78, 115, 223, 0.1);
            --secondary: #1cc88a;
            --success: #1cc88a;
            --info: #36b9cc;
            --warning: #f6c23e;
            --danger: #e74a3b;
            --dark: #2e384d;
            --light: #f8f9fc;
            --card-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
        }

        body {
            background-color: var(--light-color);
            font-family: 'Poppins', sans-serif;
            color: #444;
            line-height: 1.7;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            background: linear-gradient(135deg, rgba(46, 56, 77, 0.9), rgba(78, 115, 223, 0.85)), url('../assets/img/barangay_hero.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
        }

        /* Navbar Styling */
        .navbar-container {
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1030; /* Higher z-index to ensure it's above other elements */
            width: 100%;
        }

        .navbar {
            padding: 0.75rem 0;
        }

        .navbar-brand {
            font-weight: 700;
            letter-spacing: 0.5px;
            position: relative;
            z-index: 2;
            display: flex;
            align-items: center;
            font-size: 1.2rem;
        }

        .navbar-brand-icon {
            width: 36px;
            height: 36px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }

        .nav-item {
            margin: 0 0.25rem;
        }

        .nav-link {
            font-weight: 500;
            transition: var(--transition);
            border-radius: 8px;
            padding: 0.5rem 1rem;
            position: relative;
            z-index: 2;
            display: flex;
            align-items: center;
        }

        .nav-icon {
            width: 28px;
            height: 28px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            transition: var(--transition);
        }

        .navbar-dark .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.85);
            font-weight: 500;
            transition: var(--transition);
        }

        .navbar-dark .navbar-nav .nav-link:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .navbar-dark .navbar-nav .nav-link:hover .nav-icon {
            background: rgba(255, 255, 255, 0.2);
        }

        .navbar-dark .navbar-nav .nav-link.active {
            background-color: rgba(255, 255, 255, 0.15);
        }

        .navbar-dark .navbar-nav .nav-link.active .nav-icon {
            background: rgba(255, 255, 255, 0.2);
        }

        .navbar-toggler {
            position: relative;
            z-index: 2;
            border: none;
            padding: 0.5rem;
        }

        .register-container {
            max-width: 800px;
            margin: 3rem auto;
            animation: fadeIn 0.8s;
        }

        .card {
            border: none;
            border-radius: var(--card-border-radius);
            box-shadow: 0 0.75rem 1.5rem rgba(0, 0, 0, 0.2);
            overflow: hidden;
            backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.95);
            transform: translateY(0);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.25);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            text-align: center;
            padding: 1.5rem;
            border-radius: var(--card-border-radius) var(--card-border-radius) 0 0 !important;
            border-bottom: none;
        }

        .logo-area {
            margin-bottom: 1.5rem;
            position: relative;
            display: inline-block;
        }

        .logo-area i {
            background: rgba(255, 255, 255, 0.1);
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            font-size: 2.5rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }

        .card:hover .logo-area i {
            transform: scale(1.1);
            background: rgba(255, 255, 255, 0.2);
        }

        .card-header h3 {
            font-weight: 600;
            margin-bottom: 0.5rem;
            font-size: 1.5rem;
        }

        .card-header p {
            opacity: 0.9;
            font-size: 0.95rem;
        }

        .card-body {
            padding: 2rem;
        }

        .form-control {
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            border: 1px solid #e1e5eb;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background-color: #f8f9fc;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.1);
            background-color: #fff;
        }

        .form-select {
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            border: 1px solid #e1e5eb;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background-color: #f8f9fc;
        }

        .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.1);
            background-color: #fff;
        }

        .input-group-text {
            background-color: #f8f9fc;
            border: 1px solid #e1e5eb;
            border-right: none;
            border-radius: 0.5rem 0 0 0.5rem;
            color: var(--gray-color);
        }

        .input-group .form-control {
            border-left: none;
            border-radius: 0 0.5rem 0.5rem 0;
        }

        .form-label {
            font-weight: 500;
            color: var(--dark-color);
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .form-check-label {
            color: var(--gray-color);
            font-size: 0.9rem;
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn {
            border-radius: var(--btn-border-radius);
            padding: 0.75rem 1.25rem;
            font-weight: 500;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            font-size: 0.85rem;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            box-shadow: 0 0.25rem 0.75rem rgba(78, 115, 223, 0.2);
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
            transform: translateY(-3px);
            box-shadow: 0 0.5rem 1rem rgba(78, 115, 223, 0.3);
        }

        .password-toggle {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: var(--gray-color);
            z-index: 10;
            transition: all 0.3s ease;
        }

        .password-toggle:hover {
            color: var(--primary-color);
        }

        .alert {
            border-radius: 0.5rem;
            border: none;
            padding: 1rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
        }

        .alert-danger {
            background-color: rgba(231, 74, 59, 0.1);
            color: var(--danger-color);
        }

        .alert-success {
            background-color: rgba(28, 200, 138, 0.1);
            color: var(--secondary-color);
        }

        .alert i {
            margin-right: 0.75rem;
            font-size: 1.25rem;
        }

        .text-decoration-none {
            color: var(--primary-color);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .text-decoration-none:hover {
            color: var(--primary-dark);
        }

        .back-to-home {
            display: inline-flex;
            align-items: center;
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            transition: all 0.3s ease;
            margin-top: 1rem;
            backdrop-filter: blur(5px);
        }

        .back-to-home:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px);
            color: white;
        }

        /* Form section styling */
        .form-section {
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .form-section h5 {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
        }

        .form-section h5 i {
            margin-right: 0.75rem;
            width: 30px;
            height: 30px;
            background: rgba(78, 115, 223, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
        }

        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Header Area - New Styles */
        .header-area {
            position: relative;
            z-index: 1000;
        }

        /* Top Header */
        .top-header {
            background-color: var(--dark-color);
            padding: 0.5rem 0;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.85rem;
        }

        .top-header-info ul {
            display: flex;
            flex-wrap: wrap;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .top-header-info li {
            margin-right: 1.5rem;
            display: flex;
            align-items: center;
        }

        .top-header-info li i {
            margin-right: 0.5rem;
            color: var(--secondary-color);
        }

        .top-header-social {
            display: flex;
            justify-content: flex-end;
            gap: 0.75rem;
        }

        .top-header-social a {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
            transition: var(--transition);
        }

        .top-header-social a:hover {
            background-color: var(--secondary-color);
            color: white;
            transform: translateY(-3px);
        }

        /* Main Navbar - Updated Styles */
        .navbar-container {
            background-color: white;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .navbar-brand-wrapper {
            display: flex;
            align-items: center;
        }

        .navbar-brand-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--gradient-primary);
            color: white;
            border-radius: 12px;
            margin-right: 0.75rem;
            font-size: 1.5rem;
            box-shadow: 0 5px 15px rgba(78, 115, 223, 0.3);
        }

        .navbar-brand-text {
            display: flex;
            flex-direction: column;
        }

        .navbar-brand-title {
            font-weight: 700;
            font-size: 1.25rem;
            color: var(--dark-color);
            line-height: 1.2;
        }

        .navbar-brand-subtitle {
            font-size: 0.75rem;
            color: var(--gray-color);
            font-weight: 500;
        }

        .navbar-toggler {
            border: none;
            color: var(--dark-color);
            font-size: 1.5rem;
        }

        .navbar-toggler:focus {
            box-shadow: none;
        }

        .nav-link {
            color: var(--dark-color) !important;
            font-weight: 600;
        }

        .nav-link:hover, .nav-link.active {
            background-color: var(--primary-light);
            color: var(--primary-color) !important;
        }

        .nav-icon {
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-radius: 10px;
        }

        .nav-link:hover .nav-icon {
            background: var(--gradient-primary);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(78, 115, 223, 0.2);
        }
    </style>
</head>
<body>
    <!-- Header/Navbar -->
    <header class="header-area">
        <!-- Top Header -->
        <div class="top-header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="top-header-info">
                            <ul>
                                <li><i class="fas fa-map-marker-alt"></i> <?php echo $barangay_address ? htmlspecialchars($barangay_address) : 'Barangay Hall Address'; ?></li>
                                <li><i class="fas fa-envelope"></i> <?php echo $email ? htmlspecialchars($email) : '<EMAIL>'; ?></li>
                                <li><i class="fas fa-phone"></i> <?php echo $contact_number ? htmlspecialchars($contact_number) : 'Contact Number'; ?></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="top-header-social">
                            <?php if (!empty($facebook_url)): ?>
                            <a href="<?php echo htmlspecialchars($facebook_url); ?>" target="_blank" rel="noopener noreferrer" title="Follow us on Facebook"><i class="fab fa-facebook-f"></i></a>
                            <?php endif; ?>
                            <?php if (!empty($twitter_url)): ?>
                            <a href="<?php echo htmlspecialchars($twitter_url); ?>" target="_blank" rel="noopener noreferrer" title="Follow us on Twitter"><i class="fab fa-twitter"></i></a>
                            <?php endif; ?>
                            <?php if (!empty($instagram_url)): ?>
                            <a href="<?php echo htmlspecialchars($instagram_url); ?>" target="_blank" rel="noopener noreferrer" title="Follow us on Instagram"><i class="fab fa-instagram"></i></a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Navbar -->
        <div class="navbar-container">
            <nav class="navbar navbar-expand-lg">
                <div class="container">
                    <a class="navbar-brand" href="index.php">
                        <div class="navbar-brand-wrapper">
                            <div class="navbar-brand-icon">
                                <i class="fas fa-landmark"></i>
                            </div>
                            <div class="navbar-brand-text">
                                <span class="navbar-brand-title"><?php echo $barangay_name; ?></span>
                                <span class="navbar-brand-subtitle">Barangay Management System Resident Portal</span>
                            </div>
                        </div>
                    </a>
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                        <span class="navbar-toggler-icon"><i class="fas fa-bars"></i></span>
                    </button>
                    <div class="collapse navbar-collapse" id="navbarNav">
                        <ul class="navbar-nav ms-auto">
                            <li class="nav-item">
                                <a class="nav-link" href="login.php">
                                    <div class="nav-icon">
                                        <i class="fas fa-sign-in-alt"></i>
                                    </div>
                                    Login
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link active" href="register.php">
                                    <div class="nav-icon">
                                        <i class="fas fa-user-plus"></i>
                                    </div>
                                    Register
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <!-- Registration Form Section -->
    <div class="container register-container">
        <div class="card">
            <div class="card-header">
                <div class="logo-area">
                    <i class="fas fa-user-plus fa-4x"></i>
                </div>
                <h3>Resident Registration</h3>
                <p class="mb-0">Create your account to access <?php echo $barangay_name; ?> services</p>
            </div>
            <div class="card-body p-4">
                <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle me-3" style="font-size: 1.5rem;"></i>
                        <div>
                            <strong>Registration successful!</strong>
                            <?php if ($success_message === "success_with_email"): ?>
                                <div>We've sent a verification email to <strong><?php echo htmlspecialchars($success_email); ?></strong>.</div>
                                <div>Please check your inbox (and spam folder) to verify your account.</div>
                            <?php else: ?>
                                <div>However, we couldn't send the verification email.</div>
                                <div>Please contact the barangay administrator to verify your account manually.</div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle me-3" style="font-size: 1.5rem;"></i>
                        <div><?php echo $error_message; ?></div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" id="registration-form">
                    <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token); ?>">

                    <div class="form-section">
                        <h5><i class="fas fa-user"></i>Personal Information</h5>
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="first_name" class="form-label">First Name *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" value="<?php echo htmlspecialchars($first_name); ?>" required>
                            </div>
                            <div class="col-md-4">
                                <label for="middle_name" class="form-label">Middle Name</label>
                                <input type="text" class="form-control" id="middle_name" name="middle_name" value="<?php echo htmlspecialchars($middle_name); ?>">
                            </div>
                            <div class="col-md-4">
                                <label for="last_name" class="form-label">Last Name *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" value="<?php echo htmlspecialchars($last_name); ?>" required>
                            </div>

                            <div class="col-md-6">
                                <label for="birthdate" class="form-label">Birthdate *</label>
                                <input type="date" class="form-control" id="birthdate" name="birthdate" value="<?php echo htmlspecialchars($birthdate); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="place_of_birth" class="form-label">Place of Birth</label>
                                <input type="text" class="form-control" id="place_of_birth" name="place_of_birth" value="<?php echo htmlspecialchars($place_of_birth); ?>">
                            </div>

                            <div class="col-md-4">
                                <label for="gender" class="form-label">Gender *</label>
                                <select class="form-select" id="gender" name="gender" required>
                                    <option value="" disabled <?php echo empty($gender) ? 'selected' : ''; ?>>Select Gender</option>
                                    <option value="Male" <?php echo $gender === 'Male' ? 'selected' : ''; ?>>Male</option>
                                    <option value="Female" <?php echo $gender === 'Female' ? 'selected' : ''; ?>>Female</option>
                                    <option value="Other" <?php echo $gender === 'Other' ? 'selected' : ''; ?>>Other</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="civil_status" class="form-label">Civil Status *</label>
                                <select class="form-select" id="civil_status" name="civil_status" required>
                                    <option value="" disabled <?php echo empty($civil_status) ? 'selected' : ''; ?>>Select Status</option>
                                    <option value="Single" <?php echo $civil_status === 'Single' ? 'selected' : ''; ?>>Single</option>
                                    <option value="Married" <?php echo $civil_status === 'Married' ? 'selected' : ''; ?>>Married</option>
                                    <option value="Widowed" <?php echo $civil_status === 'Widowed' ? 'selected' : ''; ?>>Widowed</option>
                                    <option value="Separated" <?php echo $civil_status === 'Separated' ? 'selected' : ''; ?>>Separated</option>
                                    <option value="Divorced" <?php echo $civil_status === 'Divorced' ? 'selected' : ''; ?>>Divorced</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="nationality" class="form-label">Nationality</label>
                                <input type="text" class="form-control" id="nationality" name="nationality" value="<?php echo htmlspecialchars($nationality); ?>">
                            </div>

                            <div class="col-12">
                                <label for="address" class="form-label">Complete Address *</label>
                                <textarea class="form-control" id="address" name="address" rows="2" required><?php echo htmlspecialchars($address); ?></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h5><i class="fas fa-phone"></i>Contact Information</h5>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="contact_number" class="form-label">Contact Number *</label>
                                <input type="text" class="form-control" id="contact_number" name="contact_number" value="<?php echo htmlspecialchars($contact_number); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($email); ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h5><i class="fas fa-info-circle"></i>Additional Information</h5>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="occupation" class="form-label">Occupation</label>
                                <input type="text" class="form-control" id="occupation" name="occupation" value="<?php echo htmlspecialchars($occupation); ?>">
                            </div>
                            <div class="col-md-6">
                                <label for="educational_attainment" class="form-label">Educational Attainment</label>
                                <select class="form-select" id="educational_attainment" name="educational_attainment">
                                    <option value="" selected disabled>Select Educational Attainment</option>
                                    <option value="Elementary" <?php echo $educational_attainment === 'Elementary' ? 'selected' : ''; ?>>Elementary</option>
                                    <option value="High School" <?php echo $educational_attainment === 'High School' ? 'selected' : ''; ?>>High School</option>
                                    <option value="Vocational" <?php echo $educational_attainment === 'Vocational' ? 'selected' : ''; ?>>Vocational</option>
                                    <option value="College" <?php echo $educational_attainment === 'College' ? 'selected' : ''; ?>>College</option>
                                    <option value="Post Graduate" <?php echo $educational_attainment === 'Post Graduate' ? 'selected' : ''; ?>>Post Graduate</option>
                                </select>
                            </div>

                            <div class="col-md-4">
                                <label for="monthly_income" class="form-label">Monthly Income (PHP)</label>
                                <input type="number" class="form-control" id="monthly_income" name="monthly_income" value="<?php echo htmlspecialchars($monthly_income); ?>" step="0.01" min="0">
                            </div>
                            <div class="col-md-4">
                                <label for="religion" class="form-label">Religion</label>
                                <input type="text" class="form-control" id="religion" name="religion" value="<?php echo htmlspecialchars($religion); ?>">
                            </div>
                            <div class="col-md-4">
                                <label for="voter_status" class="form-label">Voter Status</label>
                                <select class="form-select" id="voter_status" name="voter_status">
                                    <option value="Not Registered" <?php echo $voter_status === 'Not Registered' ? 'selected' : ''; ?>>Not Registered</option>
                                    <option value="Registered" <?php echo $voter_status === 'Registered' ? 'selected' : ''; ?>>Registered</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h5><i class="fas fa-tags"></i>Special Categories</h5>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="is_senior" name="is_senior" value="1" <?php echo $is_senior ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_senior">Senior Citizen</label>
                                </div>
                                <div id="senior_id_field" class="mb-3 <?php echo $is_senior ? '' : 'd-none'; ?>">
                                    <label for="senior_id" class="form-label">Senior Citizen ID Number</label>
                                    <input type="text" class="form-control" id="senior_id" name="senior_id" value="<?php echo htmlspecialchars($senior_id); ?>">
                                </div>

                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="is_solo_parent" name="is_solo_parent" value="1" <?php echo $is_solo_parent ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_solo_parent">Solo Parent</label>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="is_pwd" name="is_pwd" value="1" <?php echo $is_pwd ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_pwd">Person with Disability (PWD)</label>
                                </div>
                                <div id="pwd_fields" class="<?php echo $is_pwd ? '' : 'd-none'; ?>">
                                    <div class="mb-3">
                                        <label for="pwd_type" class="form-label">Type of Disability</label>
                                        <input type="text" class="form-control" id="pwd_type" name="pwd_type" value="<?php echo htmlspecialchars($pwd_type); ?>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="pwd_id" class="form-label">PWD ID Number</label>
                                        <input type="text" class="form-control" id="pwd_id" name="pwd_id" value="<?php echo htmlspecialchars($pwd_id); ?>">
                                    </div>
                                </div>

                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_indigenous" name="is_indigenous" value="1" <?php echo $is_indigenous ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_indigenous">Indigenous People</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h5><i class="fas fa-lock"></i>Account Information</h5>
                        <div class="row g-3">
                            <div class="col-md-12">
                                <label for="username" class="form-label">Username *</label>
                                <input type="text" class="form-control" id="username" name="username" value="<?php echo htmlspecialchars($username); ?>" required>
                            </div>

                            <div class="col-md-6">
                                <label for="password" class="form-label">Password *</label>
                                <div class="position-relative">
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <span class="password-toggle" id="passwordToggle">
                                        <i class="far fa-eye"></i>
                                    </span>
                                </div>
                                <div class="form-text">Password must be at least 8 characters long.</div>
                            </div>

                            <div class="col-md-6">
                                <label for="confirm_password" class="form-label">Confirm Password *</label>
                                <div class="position-relative">
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                    <span class="password-toggle" id="confirmPasswordToggle">
                                        <i class="far fa-eye"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="terms_agreement" required>
                            <label class="form-check-label" for="terms_agreement">
                                I agree to the <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">Terms and Conditions</a> and <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">Privacy Policy</a>
                            </label>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-user-plus me-2"></i> Register
                        </button>
                    </div>
                </form>

                <div class="mt-4 text-center">
                    <p>Already have an account? <a href="login.php" class="text-decoration-none">Log in instead</a></p>
                </div>
            </div>
        </div>

        <div class="text-center mt-3">
            <a href="index.php" class="back-to-home text-decoration-none">
                <i class="fas fa-arrow-left me-2"></i> Back to Home
            </a>
        </div>
    </div>

    <!-- Terms and Conditions Modal -->
    <div class="modal fade" id="termsModal" tabindex="-1" aria-labelledby="termsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable">
            <div class="modal-content" style="border-radius: var(--card-border-radius); border: none; box-shadow: 0 0.75rem 1.5rem rgba(0, 0, 0, 0.2);">
                <div class="modal-header" style="background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)); color: white; border-bottom: none;">
                    <h5 class="modal-title" id="termsModalLabel">
                        <i class="fas fa-file-contract me-2"></i>
                        Terms and Conditions
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-4">
                        <h6 class="fw-bold"><i class="fas fa-check-circle me-2 text-primary"></i>1. Acceptance of Terms</h6>
                        <p>By registering and using the <?php echo $barangay_name; ?> Resident Portal, you agree to be bound by these Terms and Conditions.</p>
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold"><i class="fas fa-user-plus me-2 text-primary"></i>2. User Registration</h6>
                        <p>To use the portal services, you must register by providing accurate and complete information. You are responsible for maintaining the confidentiality of your account information.</p>
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold"><i class="fas fa-cogs me-2 text-primary"></i>3. Services</h6>
                        <p>The portal provides various services including but not limited to document requests, tracking, and other barangay services.</p>
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold"><i class="fas fa-user-shield me-2 text-primary"></i>4. User Conduct</h6>
                        <p>You agree to use the portal only for lawful purposes and in accordance with these Terms. You must not use the portal in any way that is unlawful, illegal, fraudulent or harmful.</p>
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold"><i class="fas fa-sync-alt me-2 text-primary"></i>5. Changes to Terms</h6>
                        <p>We reserve the right to modify these terms at any time. Continued use of the portal after any such changes constitutes your acceptance of the new terms.</p>
                    </div>
                </div>
                <div class="modal-footer" style="border-top: 1px solid rgba(0, 0, 0, 0.05);">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                        <i class="fas fa-check me-2"></i>I Understand
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Privacy Policy Modal -->
    <div class="modal fade" id="privacyModal" tabindex="-1" aria-labelledby="privacyModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable">
            <div class="modal-content" style="border-radius: var(--card-border-radius); border: none; box-shadow: 0 0.75rem 1.5rem rgba(0, 0, 0, 0.2);">
                <div class="modal-header" style="background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)); color: white; border-bottom: none;">
                    <h5 class="modal-title" id="privacyModalLabel">
                        <i class="fas fa-shield-alt me-2"></i>
                        Privacy Policy
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-4">
                        <h6 class="fw-bold"><i class="fas fa-database me-2 text-primary"></i>1. Information We Collect</h6>
                        <p>We collect personal information such as name, contact details, address, and other information necessary for providing barangay services.</p>
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold"><i class="fas fa-tasks me-2 text-primary"></i>2. How We Use Your Information</h6>
                        <p>Your information is used to provide services, improve user experience, communicate with you, and fulfill legal requirements.</p>
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold"><i class="fas fa-lock me-2 text-primary"></i>3. Data Security</h6>
                        <p>We implement measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold"><i class="fas fa-share-alt me-2 text-primary"></i>4. Data Sharing</h6>
                        <p>We may share your information with authorized barangay personnel for the purpose of processing your requests. We do not sell your data to third parties.</p>
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold"><i class="fas fa-user-shield me-2 text-primary"></i>5. Your Rights</h6>
                        <p>You have the right to access, correct, and delete your personal data. You may also withdraw consent for data processing at any time.</p>
                    </div>
                </div>
                <div class="modal-footer" style="border-top: 1px solid rgba(0, 0, 0, 0.05);">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                        <i class="fas fa-check me-2"></i>I Understand
                    </button>
                </div>
            </div>
        </div>
    </div>



    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Show/hide PWD fields
        document.getElementById('is_pwd').addEventListener('change', function() {
            const pwdFields = document.getElementById('pwd_fields');
            pwdFields.classList.toggle('d-none', !this.checked);
        });

        // Show/hide Senior ID field
        document.getElementById('is_senior').addEventListener('change', function() {
            const seniorIdField = document.getElementById('senior_id_field');
            seniorIdField.classList.toggle('d-none', !this.checked);
        });

        // Toggle password visibility
        function setupPasswordToggle(inputId, toggleId) {
            const passwordInput = document.getElementById(inputId);
            const toggleButton = document.getElementById(toggleId);

            toggleButton.addEventListener('click', function() {
                const icon = this.querySelector('i');

                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    passwordInput.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        }

        // Set up password toggles
        document.addEventListener('DOMContentLoaded', function() {
            setupPasswordToggle('password', 'passwordToggle');
            setupPasswordToggle('confirm_password', 'confirmPasswordToggle');

            // Password match validation
            const registrationForm = document.getElementById('registration-form');
            registrationForm.addEventListener('submit', function(e) {
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirm_password').value;

                if (password !== confirmPassword) {
                    e.preventDefault();
                    alert("Passwords do not match!");
                }
            });
        });
    </script>
</body>
</html>