<?php
// Initialize session with more explicit settings
session_start([
    'cookie_lifetime' => 86400,
    'cookie_httponly' => true,
    'cookie_path' => '/',
]);

// Generate a persistent token that won't be changed between page loads
if (!isset($_SESSION['persistent_csrf_token'])) {
    $_SESSION['persistent_csrf_token'] = bin2hex(random_bytes(32));
}

// Redirect to dashboard if already logged in
if (isset($_SESSION['resident_id'])) {
    header("Location: dashboard.php");
    exit;
}

// Use document root to get absolute paths
$root_path = $_SERVER['DOCUMENT_ROOT'] . '/barangay';

// Include database and utility functions
require_once $root_path . '/includes/config/database.php';
require_once $root_path . '/includes/functions/utility.php';

// Set timezone to Manila, Philippines for the entire script
date_default_timezone_set('Asia/Manila');

// Function to get and format Manila time
function getManilaTime($timestamp = null, $format = 'M d, Y g:i A') {
    try {
        if ($timestamp === null) {
            // Current time
            $dt = new DateTime('now', new DateTimeZone('Asia/Manila'));
        } elseif (is_numeric($timestamp)) {
            // Unix timestamp
            $dt = new DateTime('@' . $timestamp);
            $dt->setTimezone(new DateTimeZone('Asia/Manila'));
        } else {
            // Date string
            $dt = new DateTime($timestamp);
            $dt->setTimezone(new DateTimeZone('Asia/Manila'));
        }
        return $dt->format($format);
    } catch (Exception $e) {
        error_log("Error formatting Manila time: " . $e->getMessage());
        // Fallback to basic PHP date function
        return date($format, is_numeric($timestamp) ? $timestamp : time());
    }
}

// Get system settings
$barangay_name = "Barangay";
$barangay_address = "";
$contact_number = "";
$email = "";
$office_hours_weekday = "Monday to Friday: 8:00 AM - 5:00 PM";
$office_hours_weekend = "Saturday: 8:00 AM - 12:00 PM";
$barangay_description = "The Barangay Resident Portal provides online services to residents, making it easier to request documents and access barangay services from the comfort of your home.";
try {
    $query = "SELECT * FROM system_settings WHERE setting_name IN ('barangay_name', 'barangay_address', 'contact_number', 'email', 'office_hours_weekday', 'office_hours_weekend', 'barangay_description') LIMIT 7";
    $stmt = $conn->prepare($query);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        while ($row = $stmt->fetch()) {
            if ($row['setting_name'] == 'barangay_name') {
                $barangay_name = $row['setting_value'];
            } elseif ($row['setting_name'] == 'barangay_address') {
                $barangay_address = $row['setting_value'];
            } elseif ($row['setting_name'] == 'contact_number') {
                $contact_number = $row['setting_value'];
            } elseif ($row['setting_name'] == 'email') {
                $email = $row['setting_value'];
            } elseif ($row['setting_name'] == 'office_hours_weekday') {
                $office_hours_weekday = $row['setting_value'];
            } elseif ($row['setting_name'] == 'office_hours_weekend') {
                $office_hours_weekend = $row['setting_value'];
            } elseif ($row['setting_name'] == 'barangay_description') {
                $barangay_description = $row['setting_value'];
            }
        }
    }
} catch (PDOException $e) {
    // Default values if there's an error
}

// Get logo path using unified function
$logo_path = get_logo_path($conn, '../');

// Initialize variables
$username = $password = "";
$error_message = "";

// Use the persistent token instead of generating a new one each time
$csrf_token = $_SESSION['persistent_csrf_token'];

// Process login form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['persistent_csrf_token']) {
        $error_message = "Invalid form submission. Please try again.";
    } else {
        // Get form data
        $username = trim($_POST['username']);
        $password = $_POST['password'];

        // Basic validation
        if (empty($username) || empty($password)) {
            $error_message = "Username and password are required.";
        } else {
            try {
                // Check if username exists and account is verified
                $query = "SELECT ra.*, r.first_name, r.last_name, r.email
                        FROM resident_accounts ra
                        JOIN residents r ON ra.resident_id = r.resident_id
                        WHERE ra.username = :username
                        LIMIT 1";
                $stmt = $conn->prepare($query);
                $stmt->bindParam(':username', $username);
                $stmt->execute();

                if ($stmt->rowCount() > 0) {
                    $user = $stmt->fetch(PDO::FETCH_ASSOC);

                    // Verify password
                    if (password_verify($password, $user['password'])) {
                        // Check if account is verified
                        if ($user['is_verified'] == 1) {
                            // Set session variables
                            $_SESSION['resident_id'] = $user['resident_id'];
                            $_SESSION['username'] = $user['username'];
                            $_SESSION['email'] = $user['email'];
                            $_SESSION['first_name'] = $user['first_name'];
                            $_SESSION['last_name'] = $user['last_name'];
                            $_SESSION['full_name'] = $user['first_name'] . ' ' . $user['last_name'];

                            // Get current Manila time in database format
                            $formatted_time = getManilaTime(null, 'Y-m-d H:i:s');

                            // Update last login with Manila time
                            $query = "UPDATE resident_accounts SET last_login = :last_login WHERE id = :id";
                            $stmt = $conn->prepare($query);
                            $stmt->bindParam(':last_login', $formatted_time);
                            $stmt->bindParam(':id', $user['id']);
                            $stmt->execute();

                            // Store the formatted Manila time for session
                            $current_time = $formatted_time;

                            // Debug information
                            error_log("Updated last_login in database with Manila time: " . $formatted_time);

                            // Store last_login in session for reference
                            $_SESSION['last_login'] = $current_time;

                            // Redirect to dashboard
                            header("Location: dashboard.php");
                            exit;
                        } else {
                            // Store unverified user info in session for resend verification
                            $_SESSION['unverified_user'] = [
                                'id' => $user['id'],
                                'resident_id' => $user['resident_id'],
                                'email' => $user['email'],
                                'first_name' => $user['first_name'],
                                'last_name' => $user['last_name']
                            ];

                            $error_message = "verification_needed";
                        }
                    } else {
                        $error_message = "Invalid username or password.";
                    }
                } else {
                    $error_message = "Invalid username or password.";
                }
            } catch (PDOException $e) {
                $error_message = "An error occurred. Please try again later.";
            }
        }
    }
}

// Page title
$page_title = $barangay_name . " Resident Portal - Login";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>

    <!-- Favicon -->
    <link rel="icon" href="<?php echo get_favicon_url($conn, '../'); ?>" type="image/png">

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        :root {
            --primary-color: #4e73df;
            --primary-dark: #3a56b7;
            --primary-light: rgba(78, 115, 223, 0.1);
            --secondary-color: #1cc88a;
            --secondary-dark: #169e6c;
            --secondary-light: rgba(28, 200, 138, 0.1);
            --dark-color: #2e384d;
            --light-color: #f8f9fc;
            --gray-color: #858796;
            --danger-color: #e74a3b;
            --warning-color: #f6c23e;
            --info-color: #36b9cc;
            --card-border-radius: 1rem;
            --btn-border-radius: 0.5rem;
            --box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            --box-shadow-hover: 0 1rem 3rem rgba(0, 0, 0, 0.15);
            --transition: all 0.3s ease;
            --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            --gradient-secondary: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
            --gradient-hero: linear-gradient(135deg, rgba(46, 56, 77, 0.95), rgba(78, 115, 223, 0.9));

            /* For backward compatibility */
            --primary: #4e73df;
            --primary-light: rgba(78, 115, 223, 0.1);
            --secondary: #1cc88a;
            --success: #1cc88a;
            --info: #36b9cc;
            --warning: #f6c23e;
            --danger: #e74a3b;
            --dark: #2e384d;
            --light: #f8f9fc;
            --card-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
        }

        body {
            background-color: var(--light-color);
            font-family: 'Poppins', sans-serif;
            color: #444;
            line-height: 1.7;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            background: linear-gradient(135deg, rgba(46, 56, 77, 0.9), rgba(78, 115, 223, 0.85)), url('../assets/img/barangay_hero.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
        }

        /* Navbar Styling */
        .navbar-container {
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1030; /* Higher z-index to ensure it's above other elements */
            width: 100%;
        }

        .navbar {
            padding: 0.75rem 0;
        }

        .navbar-brand {
            font-weight: 700;
            letter-spacing: 0.5px;
            position: relative;
            z-index: 2;
            display: flex;
            align-items: center;
            font-size: 1.2rem;
        }

        .navbar-brand-icon {
            width: 36px;
            height: 36px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }

        .nav-item {
            margin: 0 0.25rem;
        }

        .nav-link {
            font-weight: 500;
            transition: var(--transition);
            border-radius: 8px;
            padding: 0.5rem 1rem;
            position: relative;
            z-index: 2;
            display: flex;
            align-items: center;
        }

        .nav-icon {
            width: 28px;
            height: 28px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            transition: var(--transition);
        }

        .navbar-dark .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.85);
            font-weight: 500;
            transition: var(--transition);
        }

        .navbar-dark .navbar-nav .nav-link:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .navbar-dark .navbar-nav .nav-link:hover .nav-icon {
            background: rgba(255, 255, 255, 0.2);
        }

        .navbar-dark .navbar-nav .nav-link.active {
            background-color: rgba(255, 255, 255, 0.15);
        }

        .navbar-dark .navbar-nav .nav-link.active .nav-icon {
            background: rgba(255, 255, 255, 0.2);
        }

        .navbar-toggler {
            position: relative;
            z-index: 2;
            border: none;
            padding: 0.5rem;
        }

        .login-container {
            max-width: 450px;
            margin: 3rem auto;
            animation: fadeIn 0.8s;
        }

        .card {
            border: none;
            border-radius: var(--card-border-radius);
            box-shadow: 0 0.75rem 1.5rem rgba(0, 0, 0, 0.2);
            overflow: hidden;
            backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.95);
            transform: translateY(0);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.25);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            text-align: center;
            padding: 1.5rem;
            border-radius: var(--card-border-radius) var(--card-border-radius) 0 0 !important;
            border-bottom: none;
        }

        .logo-area {
            margin-bottom: 1.5rem;
            position: relative;
            display: inline-block;
        }

        .logo-area i {
            background: rgba(255, 255, 255, 0.1);
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            font-size: 2.5rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }

        .card:hover .logo-area i {
            transform: scale(1.1);
            background: rgba(255, 255, 255, 0.2);
        }

        .card-header h3 {
            font-weight: 600;
            margin-bottom: 0.5rem;
            font-size: 1.5rem;
        }

        .card-header p {
            opacity: 0.9;
            font-size: 0.95rem;
        }

        .card-body {
            padding: 2rem;
        }

        .form-control {
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            border: 1px solid #e1e5eb;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background-color: #f8f9fc;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.1);
            background-color: #fff;
        }

        .input-group-text {
            background-color: #f8f9fc;
            border: 1px solid #e1e5eb;
            border-right: none;
            border-radius: 0.5rem 0 0 0.5rem;
            color: var(--gray-color);
        }

        .input-group .form-control {
            border-left: none;
            border-radius: 0 0.5rem 0.5rem 0;
        }

        .form-label {
            font-weight: 500;
            color: var(--dark-color);
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .form-check-label {
            color: var(--gray-color);
            font-size: 0.9rem;
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn {
            border-radius: var(--btn-border-radius);
            padding: 0.75rem 1.25rem;
            font-weight: 500;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            font-size: 0.85rem;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            box-shadow: 0 0.25rem 0.75rem rgba(78, 115, 223, 0.2);
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
            transform: translateY(-3px);
            box-shadow: 0 0.5rem 1rem rgba(78, 115, 223, 0.3);
        }

        .password-toggle {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: var(--gray-color);
            z-index: 10;
            transition: all 0.3s ease;
        }

        .password-toggle:hover {
            color: var(--primary-color);
        }

        .alert {
            border-radius: 0.5rem;
            border: none;
            padding: 1rem;
            margin-bottom: 1.5rem;
            position: relative;
        }

        .alert-danger {
            background-color: rgba(231, 74, 59, 0.1);
            color: var(--danger-color);
        }

        .alert i {
            margin-right: 0.75rem;
            font-size: 1.25rem;
        }

        .alert-link {
            color: var(--danger-color);
            font-weight: 600;
            text-decoration: underline;
            transition: all 0.2s ease;
        }

        .alert-link:hover {
            color: #c13321;
        }

        .btn-close {
            position: absolute;
            top: 0.75rem;
            right: 0.75rem;
            padding: 0.5rem;
            background-size: 0.65em;
        }

        .verification-message {
            line-height: 1.6;
            padding-right: 20px;
        }

        .verification-message strong {
            font-size: 1.05rem;
        }

        .text-decoration-none {
            color: var(--primary-color);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .text-decoration-none:hover {
            color: var(--primary-dark);
        }

        .back-to-home {
            display: inline-flex;
            align-items: center;
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            transition: all 0.3s ease;
            margin-top: 1rem;
            backdrop-filter: blur(5px);
        }

        .back-to-home:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px);
            color: white;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Header Area - New Styles */
        .header-area {
            position: relative;
            z-index: 1000;
        }

        /* Top Header */
        .top-header {
            background-color: var(--dark-color);
            padding: 0.5rem 0;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.85rem;
        }

        .top-header-info ul {
            display: flex;
            flex-wrap: wrap;
            list-style: none;
            margin: 0;
            padding: 0;
            justify-content: center;
        }

        .top-header-info li {
            margin-right: 1.5rem;
            display: flex;
            align-items: center;
        }

        .top-header-info li i {
            margin-right: 0.5rem;
            color: var(--secondary-color);
        }

        .top-header-social {
            display: flex;
            justify-content: flex-end;
            gap: 0.75rem;
        }

        .top-header-social a {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
            transition: var(--transition);
        }

        .top-header-social a:hover {
            background-color: var(--secondary-color);
            color: white;
            transform: translateY(-3px);
        }

        /* Main Navbar - Updated Styles */
        .navbar-container {
            background-color: white;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .navbar-brand-wrapper {
            display: flex;
            align-items: center;
        }

        .navbar-brand-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--gradient-primary);
            color: white;
            border-radius: 12px;
            margin-right: 0.75rem;
            font-size: 1.5rem;
            box-shadow: 0 5px 15px rgba(78, 115, 223, 0.3);
        }

        .navbar-brand-text {
            display: flex;
            flex-direction: column;
        }

        .navbar-brand-title {
            font-weight: 700;
            font-size: 1.25rem;
            color: var(--dark-color);
            line-height: 1.2;
        }

        .navbar-brand-subtitle {
            font-size: 0.75rem;
            color: var(--gray-color);
            font-weight: 500;
        }

        .navbar-toggler {
            border: none;
            color: var(--dark-color);
            font-size: 1.5rem;
        }

        .navbar-toggler:focus {
            box-shadow: none;
        }

        .nav-link {
            color: var(--dark-color) !important;
            font-weight: 600;
        }

        .nav-link:hover, .nav-link.active {
            background-color: var(--primary-light);
            color: var(--primary-color) !important;
        }

        .nav-icon {
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-radius: 10px;
        }

        .nav-link:hover .nav-icon {
            background: var(--gradient-primary);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(78, 115, 223, 0.2);
        }
    </style>
</head>
<body>
    <!-- Header/Navbar -->
    <header class="header-area">
        <!-- Top Header -->
        <div class="top-header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-12">
                        <div class="top-header-info">
                            <ul>
                                <li><i class="fas fa-map-marker-alt"></i> <?php echo $barangay_address ? htmlspecialchars($barangay_address) : 'Barangay Hall Address'; ?></li>
                                <li><i class="fas fa-envelope"></i> <?php echo $email ? htmlspecialchars($email) : '<EMAIL>'; ?></li>
                                <li><i class="fas fa-phone"></i> <?php echo $contact_number ? htmlspecialchars($contact_number) : 'Contact Number'; ?></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Navbar -->
        <div class="navbar-container">
            <nav class="navbar navbar-expand-lg">
                <div class="container">
                    <a class="navbar-brand" href="index.php">
                        <div class="navbar-brand-wrapper">
                            <div class="navbar-brand-icon">
                                <i class="fas fa-landmark"></i>
                            </div>
                            <div class="navbar-brand-text">
                                <span class="navbar-brand-title"><?php echo $barangay_name; ?></span>
                                <span class="navbar-brand-subtitle"><?php echo $barangay_name; ?> Resident Portal</span>
                            </div>
                        </div>
                    </a>
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                        <span class="navbar-toggler-icon"><i class="fas fa-bars"></i></span>
                    </button>
                    <div class="collapse navbar-collapse" id="navbarNav">
                        <ul class="navbar-nav ms-auto">
                            <li class="nav-item">
                                <a class="nav-link active" href="login.php">
                                    <div class="nav-icon">
                                        <i class="fas fa-sign-in-alt"></i>
                                    </div>
                                    Login
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="register.php">
                                    <div class="nav-icon">
                                        <i class="fas fa-user-plus"></i>
                                    </div>
                                    Register
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <!-- Login Form Section -->
    <div class="container login-container">
        <div class="card">
            <div class="card-header">
                <div class="logo-area">
                    <i class="fas fa-user-circle fa-4x"></i>
                </div>
                <h3>Resident Login</h3>
                <p class="mb-0">Please enter your credentials to access your account</p>
            </div>
            <div class="card-body p-4">
                <?php if (!empty($error_message)): ?>
                    <?php if ($error_message === "verification_needed"): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <div class="d-flex align-items-start">
                                <div class="me-3 pt-1">
                                    <i class="fas fa-exclamation-triangle fa-lg"></i>
                                </div>
                                <div class="verification-message">
                                    <strong>Your account has not been verified.</strong><br>
                                    Please check your email for verification instructions or
                                    <a href="resend_verification.php" class="alert-link fw-bold">click here</a>
                                    to resend the verification email.
                                </div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>

                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                    <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token); ?>">

                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                            <input type="text" class="form-control" id="username" name="username" value="<?php echo htmlspecialchars($username); ?>" placeholder="Enter your username" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <div class="input-group position-relative">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" class="form-control" id="password" name="password" placeholder="Enter your password" required>
                            <span class="password-toggle" id="passwordToggle">
                                <i class="far fa-eye"></i>
                            </span>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">Remember me</label>
                        </div>
                        <a href="forgot_password.php" class="text-decoration-none">Forgot Password?</a>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>Login
                        </button>
                    </div>
                </form>

                <div class="text-center mt-4">
                    <p class="mb-0">Don't have an account? <a href="register.php" class="text-decoration-none fw-bold">Register</a></p>
                </div>
            </div>
        </div>

        <div class="text-center mt-3">
            <a href="index.php" class="back-to-home text-decoration-none">
                <i class="fas fa-arrow-left me-2"></i> Back to Home
            </a>
        </div>
    </div>



    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle password visibility
        document.getElementById('passwordToggle').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    </script>
</body>
</html>