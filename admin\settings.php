<?php
session_start();
include '../includes/config/database.php';
include '../includes/functions/utility.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] != 'Admin') {
    header("Location: ../login.php");
    exit;
}

// Check for database connection errors
$db_error = isset($db_connection_error) && $db_connection_error;

// Initialize variables
$message = '';
$message_type = '';
$page_title = "System Settings - Barangay Management System";

// Check for session messages
if (isset($_SESSION['settings_message'])) {
    $message = $_SESSION['settings_message'];
    $message_type = $_SESSION['settings_message_type'];

    // Clear session messages
    unset($_SESSION['settings_message']);
    unset($_SESSION['settings_message_type']);
}

// Process form submission
if (isset($_POST['save_settings'])) {
    // Debug: Log the POST data
    error_log("Settings form submitted. POST data: " . print_r($_POST, true));

    // Get settings from form
    $barangay_name = trim($_POST['barangay_name']);
    $municipality = trim($_POST['municipality']);
    $province = trim($_POST['province']);
    $contact_number = trim($_POST['contact_number']);
    $email = trim($_POST['email']);
    $barangay_address = trim($_POST['barangay_address']);
    $office_hours_weekday = trim($_POST['office_hours_weekday']);
    $office_hours_weekend = trim($_POST['office_hours_weekend']);
    $barangay_description = trim($_POST['barangay_description']);
    $welcome_name = trim($_POST['welcome_name']);
    $session_timeout = (int)$_POST['session_timeout'];
    $facebook_url = trim($_POST['facebook_url']);
    $twitter_url = trim($_POST['twitter_url']);
    $instagram_url = trim($_POST['instagram_url']);
    $clearance_fee = (float)$_POST['clearance_fee'];
    $indigency_fee = (float)$_POST['indigency_fee'];
    $residency_fee = (float)$_POST['residency_fee'];
    $barangay_certificate_fee = (float)$_POST['barangay_certificate_fee'];
    $good_standing_fee = (float)$_POST['good_standing_fee'];
    $no_pending_case_fee = (float)$_POST['no_pending_case_fee'];
    $business_permit_fee = (float)$_POST['business_permit_fee'];

    // Debug: Log the processed variables
    error_log("Processed variables: barangay_name=$barangay_name, municipality=$municipality");

    // Handle document template uploads
    $template_uploads = [];
    $template_types = [
        'clearance_template' => 'Barangay Clearance',
        'certificate_template' => 'Barangay Certificate',
        'residency_template' => 'Certificate of Residency',
        'indigency_template' => 'Certificate of Indigency',
        'business_permit_template' => 'Business Permit',
        'good_standing_template' => 'Certificate of Good Standing',
        'no_pending_case_template' => 'Certificate of No Pending Case',
        'demolition_template' => 'Certificate of Demolition',
        'solo_parents_template' => 'Solo Parents Certificate',
        'toda_template' => 'TODA Certificate'
    ];

    foreach ($template_types as $field_name => $document_type) {
        if (isset($_FILES[$field_name]) && $_FILES[$field_name]['error'] == 0) {
            $allowed_types = array('application/vnd.openxmlformats-officedocument.wordprocessingml.document');
            $upload_result = upload_template_file($_FILES[$field_name], '../modules/documents/templates', $allowed_types, 10485760); // 10MB limit

            if ($upload_result['status']) {
                $template_uploads[$document_type] = $upload_result['filename'];
            } else {
                $message = $upload_result['message'];
                $message_type = 'danger';
            }
        }
    }

    // Upload logo if provided
    $logo = '';
    if (isset($_FILES['logo']) && $_FILES['logo']['error'] == 0) {
        $allowed_types = array('image/jpeg', 'image/png', 'image/gif');
        $upload_result = upload_file($_FILES['logo'], '../assets/img/logos', $allowed_types, 2097152); // 2MB limit

        if ($upload_result['status']) {
            $logo = $upload_result['filename'];
        } else {
            $message = $upload_result['message'];
            $message_type = 'danger';
        }
    }

    // Handle login background image upload
    $login_background = '';
    if (isset($_FILES['login_background']) && $_FILES['login_background']['error'] == 0) {
        $allowed_types = array('image/jpeg', 'image/png', 'image/gif');
        $upload_result = upload_file($_FILES['login_background'], '../assets/images', $allowed_types, 5242880); // 5MB limit for background

        if ($upload_result['status']) {
            $login_background = $upload_result['filename'];
        } else {
            $message = $upload_result['message'];
            $message_type = 'danger';
        }
    }

    // Handle favicon upload
    $favicon = '';
    if (isset($_FILES['favicon']) && $_FILES['favicon']['error'] == 0) {
        $allowed_types = array('image/png', 'image/x-icon', 'image/vnd.microsoft.icon', 'image/ico');
        $upload_result = upload_file($_FILES['favicon'], '../assets/images', $allowed_types, 1048576); // 1MB limit for favicon

        if ($upload_result['status']) {
            $favicon = $upload_result['filename'];
        } else {
            $message = $upload_result['message'];
            $message_type = 'danger';
        }
    }

    if (!$db_error && (empty($message) || $message_type != 'danger')) {
        // Update settings
        $settings = array(
            'barangay_name' => $barangay_name,
            'municipality' => $municipality,
            'province' => $province,
            'contact_number' => $contact_number,
            'email' => $email,
            'barangay_address' => $barangay_address,
            'office_hours_weekday' => $office_hours_weekday,
            'office_hours_weekend' => $office_hours_weekend,
            'barangay_description' => $barangay_description,
            'welcome_name' => $welcome_name,
            'session_timeout' => $session_timeout,
            'facebook_url' => $facebook_url,
            'twitter_url' => $twitter_url,
            'instagram_url' => $instagram_url,
            'clearance_fee' => $clearance_fee,
            'indigency_fee' => $indigency_fee,
            'residency_fee' => $residency_fee,
            'barangay_certificate_fee' => $barangay_certificate_fee,
            'good_standing_fee' => $good_standing_fee,
            'no_pending_case_fee' => $no_pending_case_fee,
            'business_permit_fee' => $business_permit_fee
        );

        // Add logo if uploaded
        if (!empty($logo)) {
            $settings['logo'] = $logo;
        }

        // Add login background if uploaded
        if (!empty($login_background)) {
            $settings['login_background'] = $login_background;
        }

        // Add favicon if uploaded
        if (!empty($favicon)) {
            $settings['favicon'] = $favicon;
        }

        // Add template uploads to settings with correct mapping
        foreach ($template_uploads as $document_type => $filename) {
            // Map document types to correct setting keys
            $template_key_map = [
                'Barangay Clearance' => 'barangay_clearance_template',
                'Barangay Certificate' => 'barangay_certificate_template',
                'Certificate of Residency' => 'certificate_of_residency_template',
                'Certificate of Indigency' => 'certificate_of_indigency_template',
                'Business Permit' => 'business_permit_template',
                'Certificate of Good Standing' => 'good_standing_template',
                'Certificate of No Pending Case' => 'no_pending_case_template',
                'Certificate of Demolition' => 'certificate_of_demolition_template',
                'Solo Parents Certificate' => 'solo_parents_certificate_template',
                'TODA Certificate' => 'toda_certificate_template'
            ];

            $template_key = $template_key_map[$document_type] ?? strtolower(str_replace([' ', 'Certificate of ', 'TODA '], ['_', '', 'toda_'], $document_type)) . '_template';
            $settings[$template_key] = $filename;
        }

        $success = true;
        foreach ($settings as $name => $value) {
            try {
                // First check if setting exists
                $check_query = "SELECT COUNT(*) FROM system_settings WHERE setting_name = :name";
                $check_stmt = $conn->prepare($check_query);
                $check_stmt->bindParam(':name', $name);
                $check_stmt->execute();
                $setting_exists = ($check_stmt->fetchColumn() > 0);

                if ($setting_exists) {
                    // Update existing setting
                    $query = "UPDATE system_settings SET
                                setting_value = :value,
                                updated_by = :user_id
                              WHERE setting_name = :name";

                    $stmt = $conn->prepare($query);
                    $stmt->bindParam(':value', $value);
                    $stmt->bindParam(':user_id', $_SESSION['user_id']);
                    $stmt->bindParam(':name', $name);
                } else {
                    // Insert new setting
                    $query = "INSERT INTO system_settings (setting_name, setting_value, description, updated_by)
                              VALUES (:name, :value, :description, :user_id)";

                    $description = "Setting added via admin interface";
                    $stmt = $conn->prepare($query);
                    $stmt->bindParam(':value', $value);
                    $stmt->bindParam(':user_id', $_SESSION['user_id']);
                    $stmt->bindParam(':name', $name);
                    $stmt->bindParam(':description', $description);
                }

                // Debug: Log the query execution
                error_log("Executing query for setting: $name = $value (exists: " . ($setting_exists ? 'yes' : 'no') . ")");

                $result = $stmt->execute();

                // Debug: Log the result
                error_log("Query result for $name: " . ($result ? "success" : "failed"));

                if (!$result) {
                    $success = false;
                    error_log("Error updating setting $name: " . print_r($stmt->errorInfo(), true));
                }
            } catch (PDOException $e) {
                $success = false;
                error_log("Error updating setting $name: " . $e->getMessage());
            }
        }

        if ($success) {
            // Sync document fees with document_settings table
            try {
                // Map system_settings fee names to document_settings document types
                $fee_to_document_map = [
                    'clearance_fee' => 'Barangay Clearance',
                    'indigency_fee' => 'Certificate of Indigency',
                    'residency_fee' => 'Certificate of Residency',
                    'barangay_certificate_fee' => 'Barangay Certificate',
                    'good_standing_fee' => 'Certificate of Good Standing',
                    'no_pending_case_fee' => 'Certificate of No Pending Case',
                    'demolition_fee' => 'Certificate of Demolition',
                    'solo_parents_fee' => 'Solo Parents',
                    'toda_certificate_fee' => 'TODA Certificate',
                    'business_permit_fee' => 'Business Permit'
                ];

                // Check if document_settings table exists
                $table_check = $conn->query("SHOW TABLES LIKE 'document_settings'");
                if ($table_check->rowCount() > 0) {
                    // Update each document type with the corresponding fee
                    foreach ($fee_to_document_map as $fee_name => $document_type) {
                        if (isset($settings[$fee_name])) {
                            // First check if the document type exists
                            $check_query = "SELECT COUNT(*) FROM document_settings WHERE document_type = :document_type";
                            $check_stmt = $conn->prepare($check_query);
                            $check_stmt->bindParam(':document_type', $document_type);
                            $check_stmt->execute();

                            if ($check_stmt->fetchColumn() > 0) {
                                // Update existing document
                                $update_query = "UPDATE document_settings SET fee_amount = :fee_amount WHERE document_type = :document_type";
                                $update_stmt = $conn->prepare($update_query);
                                $update_stmt->bindParam(':fee_amount', $settings[$fee_name]);
                                $update_stmt->bindParam(':document_type', $document_type);
                                $update_stmt->execute();

                                error_log("Updated fee for {$document_type} in document_settings to {$settings[$fee_name]}");
                            } else {
                                // Insert new document type
                                $insert_query = "INSERT INTO document_settings (document_type, description, fee_amount, processing_days, requirements, is_active)
                                                VALUES (:document_type, :description, :fee_amount, 1, 'Valid ID', 1)";
                                $insert_stmt = $conn->prepare($insert_query);
                                $insert_stmt->bindParam(':document_type', $document_type);
                                $description = "Official {$document_type} issued by the barangay.";
                                $insert_stmt->bindParam(':description', $description);
                                $insert_stmt->bindParam(':fee_amount', $settings[$fee_name]);
                                $insert_stmt->execute();

                                error_log("Added new document type {$document_type} to document_settings with fee {$settings[$fee_name]}");
                            }
                        }
                    }
                }
            } catch (PDOException $e) {
                // Just log the error but don't fail the transaction
                error_log("Error syncing document fees: " . $e->getMessage());
            }

            // Log the activity
            $action_type = "System Settings";
            $action_details = "Updated system settings";
            log_activity($conn, $_SESSION['user_id'], $action_type, $action_details, 'admin');

            // Update session variables with new values to reflect changes immediately
            $_SESSION['barangay_name'] = $barangay_name;
            $_SESSION['municipality'] = $municipality;
            $_SESSION['province'] = $province;
            $_SESSION['welcome_name'] = $welcome_name;

            // Update logo in session if uploaded
            if (!empty($logo)) {
                $_SESSION['logo'] = $logo;
                // Set a timestamp to prevent caching
                $_SESSION['logo_timestamp'] = time();
            }

            // Update favicon in session if uploaded
            if (!empty($favicon)) {
                $_SESSION['favicon'] = $favicon;
                $_SESSION['favicon_timestamp'] = time();
            }

            // Update login background in session if uploaded
            if (!empty($login_background)) {
                $_SESSION['login_background'] = $login_background;
                $_SESSION['login_background_timestamp'] = time();
            }

            $message = "System settings have been successfully updated.";
            $message_type = "success";

            // Store success message in session for display after refresh
            $_SESSION['settings_message'] = $message;
            $_SESSION['settings_message_type'] = $message_type;

            // Redirect to refresh the page and show updated sidebar
            header("Location: " . $_SERVER['PHP_SELF']);
            exit;
        } else {
            $message = "Error updating some settings. Please try again.";
            $message_type = "danger";
        }
    }
}

// Get current settings
$settings = array();
if (!$db_error) {
    try {
        $query = "SELECT * FROM system_settings";
        $stmt = $conn->prepare($query);
        $stmt->execute();

        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $settings[$row['setting_name']] = $row['setting_value'];
        }
    } catch (PDOException $e) {
        error_log("Error fetching settings: " . $e->getMessage());
        $message = "Error loading settings. Please try again.";
        $message_type = "danger";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>

    <!-- Favicon -->
    <link rel="icon" href="<?php echo get_favicon_url($conn, '../'); ?>" type="image/png">

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .time-picker-container {
            position: relative;
        }
        .time-picker-container .form-control {
            background-color: #fff;
            cursor: pointer;
        }
        .time-picker-container .input-group-text {
            cursor: pointer;
        }
        .office-hours-container {
            background-color: #f8f9fa;
            border-radius: 0.5rem;
            padding: 1.25rem;
            margin-bottom: 1.5rem;
            border: 1px solid #e9ecef;
        }
        .office-hours-container h6 {
            margin-bottom: 1rem;
            color: #4e73df;
            font-weight: 600;
        }
        .time-range-group {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
        }
        .time-range-group .time-separator {
            margin: 0 10px;
            font-weight: bold;
        }
        .day-label {
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #5a5c69;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../includes/sidebar.php'; ?>

            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="fas fa-cogs me-2"></i> System Settings</h1>
                </div>

                <?php if ($db_error): ?>
                <div class="alert alert-danger">
                    <strong>Database Error:</strong> Could not connect to the database. Please contact the system administrator.
                </div>
                <?php else: ?>

                <!-- Remove the static alert message and only keep toast -->
                <?php /* if (!empty($message)): ?>
                <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; */ ?>

                <!-- Settings Form -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">System Configuration</h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data">
                            <!-- Nav tabs -->
                            <ul class="nav nav-tabs nav-fill mb-4" id="settingsTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active d-flex align-items-center justify-content-center" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">
                                        <i class="fas fa-cog me-2"></i> General
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link d-flex align-items-center justify-content-center" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab" aria-controls="contact" aria-selected="false">
                                        <i class="fas fa-address-card me-2"></i> Contact Information
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link d-flex align-items-center justify-content-center" id="fees-tab" data-bs-toggle="tab" data-bs-target="#fees" type="button" role="tab" aria-controls="fees" aria-selected="false">
                                        <i class="fas fa-file-invoice-dollar me-2"></i> Document Fees
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link d-flex align-items-center justify-content-center" id="appearance-tab" data-bs-toggle="tab" data-bs-target="#appearance" type="button" role="tab" aria-controls="appearance" aria-selected="false">
                                        <i class="fas fa-palette me-2"></i> Appearance
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link d-flex align-items-center justify-content-center" id="templates-tab" data-bs-toggle="tab" data-bs-target="#templates" type="button" role="tab" aria-controls="templates" aria-selected="false">
                                        <i class="fas fa-file-word me-2"></i> Document Templates
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link d-flex align-items-center justify-content-center" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab" aria-controls="security" aria-selected="false">
                                        <i class="fas fa-shield-alt me-2"></i> Security
                                    </button>
                                </li>
                            </ul>
                            <style>
                                .nav-tabs .nav-link {
                                    color: #495057;
                                    border: 1px solid #dee2e6;
                                    border-radius: 0;
                                    padding: 0.75rem 1rem;
                                    font-weight: 500;
                                    transition: all 0.2s ease;
                                }
                                .nav-tabs .nav-link:hover {
                                    background-color: #f8f9fa;
                                    border-color: #dee2e6;
                                }
                                .nav-tabs .nav-link.active {
                                    color: #0d6efd;
                                    background-color: #fff;
                                    border-color: #dee2e6 #dee2e6 #fff;
                                    border-bottom: 3px solid #0d6efd;
                                }
                                .tab-content {
                                    padding-top: 1.5rem;
                                }
                                .template-upload-card {
                                    transition: transform 0.2s ease;
                                }
                                .template-upload-card:hover {
                                    transform: translateY(-2px);
                                }
                                .file-input-wrapper {
                                    position: relative;
                                    overflow: hidden;
                                    display: inline-block;
                                    width: 100%;
                                }
                                .file-input-wrapper input[type=file] {
                                    position: absolute;
                                    left: -9999px;
                                }
                                .file-input-label {
                                    cursor: pointer;
                                    display: block;
                                    padding: 0.375rem 0.75rem;
                                    border: 1px solid #ced4da;
                                    border-radius: 0.375rem;
                                    background-color: #fff;
                                    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
                                }
                                .file-input-label:hover {
                                    border-color: #86b7fe;
                                    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
                                }
                            </style>

                            <!-- Tab content -->
                            <div class="tab-content mt-4" id="settingsTabContent">
                                <!-- General Settings -->
                                <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="barangay_name" class="form-label">Barangay Name</label>
                                            <input type="text" class="form-control" id="barangay_name" name="barangay_name" value="<?php echo isset($settings['barangay_name']) ? $settings['barangay_name'] : ''; ?>" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="municipality" class="form-label">Municipality/City</label>
                                            <input type="text" class="form-control" id="municipality" name="municipality" value="<?php echo isset($settings['municipality']) ? $settings['municipality'] : ''; ?>" required>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="province" class="form-label">Province</label>
                                            <input type="text" class="form-control" id="province" name="province" value="<?php echo isset($settings['province']) ? $settings['province'] : ''; ?>" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="welcome_name" class="form-label">Welcome Message Name</label>
                                            <input type="text" class="form-control" id="welcome_name" name="welcome_name" value="<?php echo isset($settings['welcome_name']) ? $settings['welcome_name'] : 'Talisaynon'; ?>" required>
                                            
                                        </div>
                                    </div>
                                </div>

                                <!-- Contact Information Settings -->
                                <div class="tab-pane fade" id="contact" role="tabpanel" aria-labelledby="contact-tab">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="contact_number" class="form-label">Contact Number</label>
                                            <input type="text" class="form-control" id="contact_number" name="contact_number" value="<?php echo isset($settings['contact_number']) ? $settings['contact_number'] : ''; ?>" required>
                                            <small class="form-text text-muted">This will be displayed on the resident portal</small>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label">Email Address</label>
                                            <input type="email" class="form-control" id="email" name="email" value="<?php echo isset($settings['email']) ? $settings['email'] : ''; ?>" required>
                                            <small class="form-text text-muted">This will be displayed on the resident portal</small>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="barangay_address" class="form-label">Barangay Address</label>
                                        <textarea class="form-control" id="barangay_address" name="barangay_address" rows="2"><?php echo isset($settings['barangay_address']) ? $settings['barangay_address'] : ''; ?></textarea>
                                        <small class="form-text text-muted">Complete address of the barangay hall</small>
                                    </div>

                                    <div class="mb-4">
                                        <label for="barangay_description" class="form-label">About Barangay</label>
                                        <textarea class="form-control" id="barangay_description" name="barangay_description" rows="4"><?php echo isset($settings['barangay_description']) ? $settings['barangay_description'] : 'The Barangay Resident Portal provides online services to residents, making it easier to request documents and access barangay services from the comfort of your home.'; ?></textarea>
                                        <small class="form-text text-muted">This description will appear on the resident portal homepage</small>
                                    </div>

                                    <!-- Social Media URLs -->
                                    <div class="card mb-4">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0"><i class="fab fa-facebook me-2"></i>Social Media Links</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-4 mb-3">
                                                    <label for="facebook_url" class="form-label"><i class="fab fa-facebook-f me-2"></i>Facebook URL</label>
                                                    <input type="url" class="form-control" id="facebook_url" name="facebook_url" value="<?php echo isset($settings['facebook_url']) ? $settings['facebook_url'] : ''; ?>" placeholder="https://facebook.com/your-page">
                                                    <small class="form-text text-muted">Complete Facebook page URL</small>
                                                </div>
                                                <div class="col-md-4 mb-3">
                                                    <label for="twitter_url" class="form-label"><i class="fab fa-twitter me-2"></i>Twitter URL</label>
                                                    <input type="url" class="form-control" id="twitter_url" name="twitter_url" value="<?php echo isset($settings['twitter_url']) ? $settings['twitter_url'] : ''; ?>" placeholder="https://twitter.com/your-account">
                                                    <small class="form-text text-muted">Complete Twitter profile URL</small>
                                                </div>
                                                <div class="col-md-4 mb-3">
                                                    <label for="instagram_url" class="form-label"><i class="fab fa-instagram me-2"></i>Instagram URL</label>
                                                    <input type="url" class="form-control" id="instagram_url" name="instagram_url" value="<?php echo isset($settings['instagram_url']) ? $settings['instagram_url'] : ''; ?>" placeholder="https://instagram.com/your-account">
                                                    <small class="form-text text-muted">Complete Instagram profile URL</small>
                                                </div>
                                            </div>
                                            <div class="alert alert-info mt-3">
                                                <i class="fas fa-info-circle me-2"></i>
                                                <small>These social media links will appear in the footer of the resident portal. Leave blank to hide the respective social media icon.</small>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Weekday Office Hours -->
                                    <div class="office-hours-container">
                                        <h6><i class="fas fa-calendar-week me-2"></i>Weekday Office Hours</h6>
                                        <div class="day-label">Monday to Friday</div>
                                        <div class="row">
                                            <div class="col-md-5">
                                                <div class="mb-3">
                                                    <label for="weekday_start_time" class="form-label">Opening Time</label>
                                                    <div class="input-group time-picker-container">
                                                        <input type="text" class="form-control time-picker" id="weekday_start_time" name="weekday_start_time" placeholder="Select time"
                                                            value="<?php
                                                                $weekday_hours = isset($settings['office_hours_weekday']) ? $settings['office_hours_weekday'] : 'Monday to Friday: 8:00 AM - 5:00 PM';
                                                                preg_match('/(\d+:\d+\s*[AP]M)\s*-/', $weekday_hours, $matches);
                                                                echo isset($matches[1]) ? $matches[1] : '8:00 AM';
                                                            ?>" required>
                                                        <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-2 d-flex align-items-end justify-content-center mb-3">
                                                <span class="time-separator">to</span>
                                            </div>
                                            <div class="col-md-5">
                                                <div class="mb-3">
                                                    <label for="weekday_end_time" class="form-label">Closing Time</label>
                                                    <div class="input-group time-picker-container">
                                                        <input type="text" class="form-control time-picker" id="weekday_end_time" name="weekday_end_time" placeholder="Select time"
                                                            value="<?php
                                                                preg_match('/-\s*(\d+:\d+\s*[AP]M)/', $weekday_hours, $matches);
                                                                echo isset($matches[1]) ? $matches[1] : '5:00 PM';
                                                            ?>" required>
                                                        <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <input type="hidden" id="office_hours_weekday" name="office_hours_weekday" value="<?php echo isset($settings['office_hours_weekday']) ? $settings['office_hours_weekday'] : 'Monday to Friday: 8:00 AM - 5:00 PM'; ?>">
                                    </div>

                                    <!-- Weekend Office Hours -->
                                    <div class="office-hours-container">
                                        <h6><i class="fas fa-calendar-day me-2"></i>Weekend Office Hours</h6>
                                        <div class="day-label">Saturday</div>
                                        <div class="row">
                                            <div class="col-md-5">
                                                <div class="mb-3">
                                                    <label for="weekend_start_time" class="form-label">Opening Time</label>
                                                    <div class="input-group time-picker-container">
                                                        <input type="text" class="form-control time-picker" id="weekend_start_time" name="weekend_start_time" placeholder="Select time"
                                                            value="<?php
                                                                $weekend_hours = isset($settings['office_hours_weekend']) ? $settings['office_hours_weekend'] : 'Saturday: 8:00 AM - 12:00 PM';
                                                                preg_match('/(\d+:\d+\s*[AP]M)\s*-/', $weekend_hours, $matches);
                                                                echo isset($matches[1]) ? $matches[1] : '8:00 AM';
                                                            ?>" required>
                                                        <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-2 d-flex align-items-end justify-content-center mb-3">
                                                <span class="time-separator">to</span>
                                            </div>
                                            <div class="col-md-5">
                                                <div class="mb-3">
                                                    <label for="weekend_end_time" class="form-label">Closing Time</label>
                                                    <div class="input-group time-picker-container">
                                                        <input type="text" class="form-control time-picker" id="weekend_end_time" name="weekend_end_time" placeholder="Select time"
                                                            value="<?php
                                                                preg_match('/-\s*(\d+:\d+\s*[AP]M)/', $weekend_hours, $matches);
                                                                echo isset($matches[1]) ? $matches[1] : '12:00 PM';
                                                            ?>" required>
                                                        <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <input type="hidden" id="office_hours_weekend" name="office_hours_weekend" value="<?php echo isset($settings['office_hours_weekend']) ? $settings['office_hours_weekend'] : 'Saturday: 8:00 AM - 12:00 PM'; ?>">
                                    </div>
                                </div>

                                <!-- Document Fees Settings -->
                                <div class="tab-pane fade" id="fees" role="tabpanel" aria-labelledby="fees-tab">
                                    <div class="card mb-4">
                                        <div class="card-header bg-light">
                                            <h5 class="mb-0"><i class="fas fa-file-invoice-dollar me-2"></i>Document Fee Configuration</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="clearance_fee" class="form-label fw-bold">Barangay Clearance Fee</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">₱</span>
                                                        <input type="number" class="form-control" id="clearance_fee" name="clearance_fee" step="0.01" min="0" value="<?php echo isset($settings['clearance_fee']) ? $settings['clearance_fee'] : '0.00'; ?>" required>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="indigency_fee" class="form-label fw-bold">Certificate of Indigency Fee</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">₱</span>
                                                        <input type="number" class="form-control" id="indigency_fee" name="indigency_fee" step="0.01" min="0" value="<?php echo isset($settings['indigency_fee']) ? $settings['indigency_fee'] : '0.00'; ?>" required>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="residency_fee" class="form-label fw-bold">Certificate of Residency Fee</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">₱</span>
                                                        <input type="number" class="form-control" id="residency_fee" name="residency_fee" step="0.01" min="0" value="<?php echo isset($settings['residency_fee']) ? $settings['residency_fee'] : '0.00'; ?>" required>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="barangay_certificate_fee" class="form-label fw-bold">Barangay Certificate Fee</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">₱</span>
                                                        <input type="number" class="form-control" id="barangay_certificate_fee" name="barangay_certificate_fee" step="0.01" min="0" value="<?php echo isset($settings['barangay_certificate_fee']) ? $settings['barangay_certificate_fee'] : '0.00'; ?>" required>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="good_standing_fee" class="form-label fw-bold">Certificate of Good Standing Fee</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">₱</span>
                                                        <input type="number" class="form-control" id="good_standing_fee" name="good_standing_fee" step="0.01" min="0" value="<?php echo isset($settings['good_standing_fee']) ? $settings['good_standing_fee'] : '50.00'; ?>" required>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="no_pending_case_fee" class="form-label fw-bold">Certificate of No Pending Case Fee</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">₱</span>
                                                        <input type="number" class="form-control" id="no_pending_case_fee" name="no_pending_case_fee" step="0.01" min="0" value="<?php echo isset($settings['no_pending_case_fee']) ? $settings['no_pending_case_fee'] : '50.00'; ?>" required>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="demolition_fee" class="form-label fw-bold">Certificate of Demolition Fee</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">₱</span>
                                                        <input type="number" class="form-control" id="demolition_fee" name="demolition_fee" step="0.01" min="0" value="<?php echo isset($settings['demolition_fee']) ? $settings['demolition_fee'] : '100.00'; ?>" required>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="solo_parents_fee" class="form-label fw-bold">Solo Parents Certificate Fee</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">₱</span>
                                                        <input type="number" class="form-control" id="solo_parents_fee" name="solo_parents_fee" step="0.01" min="0" value="<?php echo isset($settings['solo_parents_fee']) ? $settings['solo_parents_fee'] : '50.00'; ?>" required>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="toda_certificate_fee" class="form-label fw-bold">TODA Certificate Fee</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">₱</span>
                                                        <input type="number" class="form-control" id="toda_certificate_fee" name="toda_certificate_fee" step="0.01" min="0" value="<?php echo isset($settings['toda_certificate_fee']) ? $settings['toda_certificate_fee'] : '150.00'; ?>" required>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="business_permit_fee" class="form-label fw-bold">Business Permit Fee</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">₱</span>
                                                        <input type="number" class="form-control" id="business_permit_fee" name="business_permit_fee" step="0.01" min="0" value="<?php echo isset($settings['business_permit_fee']) ? $settings['business_permit_fee'] : '200.00'; ?>" required>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="alert alert-info mt-3">
                                                <i class="fas fa-info-circle me-2"></i>
                                                <small>These fees will be automatically applied when residents request documents through the resident portal.</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Appearance Settings -->
                                <div class="tab-pane fade" id="appearance" role="tabpanel" aria-labelledby="appearance-tab">
                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <label for="logo" class="form-label">Barangay Logo</label>
                                            <?php if (!empty($settings['logo'])): ?>
                                            <div class="mb-3 border p-3 rounded text-center bg-light">
                                                <img src="../assets/img/logos/<?php echo $settings['logo']; ?>" alt="Barangay Logo" class="img-thumbnail" style="max-width: 150px; max-height: 150px;">
                                                <p class="mt-2 mb-0 text-muted">Current Logo</p>
                                            </div>
                                            <?php endif; ?>
                                            <div class="input-group mb-2">
                                                <input type="file" class="form-control" id="logo" name="logo" accept="image/jpeg,image/png,image/gif">
                                                <label class="input-group-text" for="logo"><i class="fas fa-upload"></i></label>
                                            </div>
                                            <div class="form-text">
                                                <small class="text-muted">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    Accepted formats: JPEG, PNG, or GIF, max 2MB. Square images work best.
                                                    <br>
                                                    The logo will be displayed in the sidebar and on printed documents.
                                                </small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="system_name" class="form-label">System Name</label>
                                            <input type="text" class="form-control" id="system_name" name="system_name" value="<?php echo isset($settings['system_name']) ? $settings['system_name'] : 'Barangay Management System'; ?>" required>
                                            <div class="form-text">
                                                <small class="text-muted">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    The system name appears in the browser tab and other locations.
                                                </small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <label for="login_background" class="form-label">Login Page Background Image</label>
                                            <?php if (!empty($settings['login_background'])): ?>
                                            <div class="mb-3 border p-3 rounded text-center bg-light">
                                                <img src="../assets/images/<?php echo $settings['login_background']; ?>" alt="Login Background" class="img-thumbnail" style="max-width: 300px; max-height: 200px;">
                                                <p class="mt-2 mb-0 text-muted">Current Login Background</p>
                                            </div>
                                            <?php endif; ?>
                                            <div class="input-group mb-2">
                                                <input type="file" class="form-control" id="login_background" name="login_background" accept="image/jpeg,image/png,image/gif">
                                                <label class="input-group-text" for="login_background"><i class="fas fa-upload"></i></label>
                                            </div>
                                            <div class="form-text">
                                                <small class="text-muted">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    Accepted formats: JPEG, PNG, or GIF, max 5MB. Landscape images (1920x1080 or similar) work best.
                                                    <br>
                                                    This image will be used as the background for the login page.
                                                </small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="favicon" class="form-label">Website Favicon</label>
                                            <?php if (!empty($settings['favicon'])): ?>
                                            <div class="mb-3 border p-3 rounded text-center bg-light">
                                                <img src="../assets/images/<?php echo $settings['favicon']; ?>" alt="Current Favicon" class="img-thumbnail" style="max-width: 64px; max-height: 64px;">
                                                <p class="mt-2 mb-0 text-muted">Current Favicon</p>
                                            </div>
                                            <?php endif; ?>
                                            <div class="input-group mb-2">
                                                <input type="file" class="form-control" id="favicon" name="favicon" accept="image/png,image/x-icon,image/vnd.microsoft.icon,image/ico">
                                                <label class="input-group-text" for="favicon"><i class="fas fa-upload"></i></label>
                                            </div>
                                            <div class="form-text">
                                                <small class="text-muted">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    Accepted formats: PNG or ICO, max 1MB. Square images (16x16, 32x32, or 64x64 pixels) work best.
                                                    <br>
                                                    This icon will appear in browser tabs and bookmarks.
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Document Templates Settings -->
                                <div class="tab-pane fade" id="templates" role="tabpanel" aria-labelledby="templates-tab">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Document Templates Management</strong><br>
                                        Upload custom Word document templates (.docx) for each certificate type. These templates will be used when generating documents for residents.
                                        Templates should contain placeholders that will be replaced with actual resident data.
                                        <br><br>
                                        <a href="template_placeholder_guide.php" class="btn btn-sm btn-outline-info" target="_blank">
                                            <i class="fas fa-code me-1"></i>View Placeholder Guide
                                        </a>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-4">
                                            <div class="card">
                                                <div class="card-header bg-primary text-white">
                                                    <h6 class="mb-0"><i class="fas fa-certificate me-2"></i>Basic Certificates</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="mb-3">
                                                        <label for="clearance_template" class="form-label">
                                                            <i class="fas fa-file-word text-primary me-2"></i>Barangay Clearance Template
                                                        </label>
                                                        <input type="file" class="form-control" id="clearance_template" name="clearance_template" accept=".docx">
                                                        <small class="form-text text-muted">Current: <?php echo isset($settings['barangay_clearance_template']) ? $settings['barangay_clearance_template'] : 'Default template'; ?></small>
                                                    </div>

                                                    <div class="mb-3">
                                                        <label for="certificate_template" class="form-label">
                                                            <i class="fas fa-file-word text-primary me-2"></i>Barangay Certificate Template
                                                        </label>
                                                        <input type="file" class="form-control" id="certificate_template" name="certificate_template" accept=".docx">
                                                        <small class="form-text text-muted">Current: <?php echo isset($settings['barangay_certificate_template']) ? $settings['barangay_certificate_template'] : 'Default template'; ?></small>
                                                    </div>

                                                    <div class="mb-3">
                                                        <label for="residency_template" class="form-label">
                                                            <i class="fas fa-file-word text-primary me-2"></i>Certificate of Residency Template
                                                        </label>
                                                        <input type="file" class="form-control" id="residency_template" name="residency_template" accept=".docx">
                                                        <small class="form-text text-muted">Current: <?php echo isset($settings['certificate_of_residency_template']) ? $settings['certificate_of_residency_template'] : 'Default template'; ?></small>
                                                    </div>

                                                    <div class="mb-3">
                                                        <label for="indigency_template" class="form-label">
                                                            <i class="fas fa-file-word text-primary me-2"></i>Certificate of Indigency Template
                                                        </label>
                                                        <input type="file" class="form-control" id="indigency_template" name="indigency_template" accept=".docx">
                                                        <small class="form-text text-muted">Current: <?php echo isset($settings['certificate_of_indigency_template']) ? $settings['certificate_of_indigency_template'] : 'Default template'; ?></small>
                                                    </div>

                                                    <div class="mb-3">
                                                        <label for="business_permit_template" class="form-label">
                                                            <i class="fas fa-file-word text-primary me-2"></i>Business Permit Template
                                                        </label>
                                                        <input type="file" class="form-control" id="business_permit_template" name="business_permit_template" accept=".docx">
                                                        <small class="form-text text-muted">Current: <?php echo isset($settings['business_permit_template']) ? $settings['business_permit_template'] : 'Default template'; ?></small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-4">
                                            <div class="card">
                                                <div class="card-header bg-success text-white">
                                                    <h6 class="mb-0"><i class="fas fa-award me-2"></i>Special Certificates</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="mb-3">
                                                        <label for="good_standing_template" class="form-label">
                                                            <i class="fas fa-file-word text-success me-2"></i>Certificate of Good Standing Template
                                                        </label>
                                                        <input type="file" class="form-control" id="good_standing_template" name="good_standing_template" accept=".docx">
                                                        <small class="form-text text-muted">Current: <?php echo isset($settings['good_standing_template']) ? $settings['good_standing_template'] : 'Default template'; ?></small>
                                                    </div>

                                                    <div class="mb-3">
                                                        <label for="no_pending_case_template" class="form-label">
                                                            <i class="fas fa-file-word text-success me-2"></i>Certificate of No Pending Case Template
                                                        </label>
                                                        <input type="file" class="form-control" id="no_pending_case_template" name="no_pending_case_template" accept=".docx">
                                                        <small class="form-text text-muted">Current: <?php echo isset($settings['no_pending_case_template']) ? $settings['no_pending_case_template'] : 'Default template'; ?></small>
                                                    </div>

                                                    <div class="mb-3">
                                                        <label for="demolition_template" class="form-label">
                                                            <i class="fas fa-file-word text-success me-2"></i>Certificate of Demolition Template
                                                        </label>
                                                        <input type="file" class="form-control" id="demolition_template" name="demolition_template" accept=".docx">
                                                        <small class="form-text text-muted">Current: <?php echo isset($settings['certificate_of_demolition_template']) ? $settings['certificate_of_demolition_template'] : 'Default template'; ?></small>
                                                    </div>

                                                    <div class="mb-3">
                                                        <label for="solo_parents_template" class="form-label">
                                                            <i class="fas fa-file-word text-success me-2"></i>Solo Parents Certificate Template
                                                        </label>
                                                        <input type="file" class="form-control" id="solo_parents_template" name="solo_parents_template" accept=".docx">
                                                        <small class="form-text text-muted">Current: <?php echo isset($settings['solo_parents_certificate_template']) ? $settings['solo_parents_certificate_template'] : 'Default template'; ?></small>
                                                    </div>

                                                    <div class="mb-3">
                                                        <label for="toda_template" class="form-label">
                                                            <i class="fas fa-file-word text-success me-2"></i>TODA Certificate Template
                                                        </label>
                                                        <input type="file" class="form-control" id="toda_template" name="toda_template" accept=".docx">
                                                        <small class="form-text text-muted">Current: <?php echo isset($settings['toda_certificate_template']) ? $settings['toda_certificate_template'] : 'Default template'; ?></small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="alert alert-success">
                                        <i class="fas fa-magic me-2"></i>
                                        <strong>✅ Smart Template System:</strong>
                                        <p class="mb-2">When residents request documents, placeholders in your templates are automatically replaced with their actual data!</p>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <strong>Common Placeholders:</strong>
                                                <ul class="small mb-2">
                                                    <li><code>{{RESIDENT_NAME}}</code> - Full name</li>
                                                    <li><code>{{DATE}}</code> - Current date</li>
                                                    <li><code>{{PURPOSE}}</code> - Document purpose</li>
                                                    <li><code>{{BARANGAY_NAME}}</code> - Barangay name</li>
                                                    <li><code>{{FULL_ADDRESS}}</code> - Complete address</li>
                                                </ul>
                                            </div>
                                            <div class="col-md-6">
                                                <strong>Example Usage:</strong>
                                                <div class="bg-light p-2 rounded small">
                                                    This is to certify that <code>{{RESIDENT_NAME}}</code>,
                                                    a resident of <code>{{FULL_ADDRESS}}</code>,
                                                    is requesting this document for <code>{{PURPOSE}}</code>
                                                    on <code>{{DATE}}</code>.
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt-2">
                                            <a href="test_template_with_placeholders.php" class="btn btn-sm btn-outline-success me-2">
                                                <i class="fas fa-vial me-1"></i>Test Placeholders
                                            </a>
                                            <a href="template_placeholder_guide.php" class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-book me-1"></i>Full Guide
                                            </a>
                                        </div>
                                    </div>

                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <strong>Upload Requirements:</strong>
                                        <ul class="mb-0 mt-2">
                                            <li>Only .docx (Word 2007+) files are supported</li>
                                            <li>Maximum file size: 10MB per template</li>
                                            <li>Templates will replace existing ones when uploaded</li>
                                            <li>Keep backup copies of your templates</li>
                                        </ul>
                                    </div>
                                </div>

                                <!-- Security Settings -->
                                <div class="tab-pane fade" id="security" role="tabpanel" aria-labelledby="security-tab">
                                    <div class="mb-3">
                                        <label for="session_timeout" class="form-label">Session Timeout (seconds)</label>
                                        <input type="number" class="form-control" id="session_timeout" name="session_timeout" min="300" value="<?php echo isset($settings['session_timeout']) ? $settings['session_timeout'] : '1800'; ?>" required>
                                        <small class="form-text text-muted">Minimum 300 seconds (5 minutes)</small>
                                    </div>
                                </div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                                <button type="reset" class="btn btn-secondary" id="resetBtn">
                                    <i class="fas fa-undo me-1"></i> Reset
                                </button>
                                <button type="submit" name="save_settings" class="btn btn-primary" id="saveBtn">
                                    <i class="fas fa-save me-1"></i> Save Settings
                                </button>
                            </div>
                            <style>
                                #resetBtn, #saveBtn {
                                    padding: 0.5rem 1.5rem;
                                    font-weight: 500;
                                    border-radius: 0.375rem;
                                    transition: all 0.3s ease;
                                }
                                #resetBtn:hover {
                                    background-color: #6c757d;
                                    transform: translateY(-2px);
                                    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                                }
                                #saveBtn:hover {
                                    background-color: #0b5ed7;
                                    transform: translateY(-2px);
                                    box-shadow: 0 4px 8px rgba(13,110,253,0.25);
                                }
                            </style>
                        </form>
                    </div>
                </div>

                <?php endif; ?>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script>
        $(document).ready(function() {
            // Initialize time pickers
            flatpickr(".time-picker", {
                enableTime: true,
                noCalendar: true,
                dateFormat: "h:i K", // 12-hour format with AM/PM
                time_24hr: false,
                minuteIncrement: 15,
                defaultHour: 8,
                onChange: function(selectedDates, dateStr, instance) {
                    updateOfficeHours();
                }
            });

            // Function to update the hidden office hours fields
            function updateOfficeHours() {
                // Update weekday office hours
                const weekdayStartTime = $('#weekday_start_time').val();
                const weekdayEndTime = $('#weekday_end_time').val();
                if (weekdayStartTime && weekdayEndTime) {
                    $('#office_hours_weekday').val(`Monday to Friday: ${weekdayStartTime} - ${weekdayEndTime}`);
                }

                // Update weekend office hours
                const weekendStartTime = $('#weekend_start_time').val();
                const weekendEndTime = $('#weekend_end_time').val();
                if (weekendStartTime && weekendEndTime) {
                    $('#office_hours_weekend').val(`Saturday: ${weekendStartTime} - ${weekendEndTime}`);
                }
            }

            // Add event listeners to time inputs
            $('.time-picker').on('change', updateOfficeHours);

            // Initialize office hours on page load
            updateOfficeHours();

            // Show toast if message exists
            <?php if (!empty($message)): ?>
            showToast('<?php echo addslashes($message); ?>', '<?php echo $message_type; ?>');
            <?php endif; ?>

            // Add form submission handler
            $('form').on('submit', function(e) {
                console.log('Form submitted');
                // Store current logo preview for potential use after form submission
                let currentLogoSrc = $('.sidebar img.rounded-circle').attr('src');
                // Store this in a localStorage variable so it persists after page reload
                if (currentLogoSrc && $('#logo').val()) {
                    localStorage.setItem('lastUploadedLogo', currentLogoSrc);
                    localStorage.setItem('logoUpdateTime', new Date().getTime());
                }

                // Show loading state
                $('button[name="save_settings"]').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...');
            });

            // Add explicit handler for Save Settings button click
            $('#saveBtn').on('click', function() {
                console.log('Save button clicked');
                // Make sure the form has the correct elements
                if ($('input[name="save_settings"]').length === 0) {
                    $('<input>').attr({
                        type: 'hidden',
                        name: 'save_settings',
                        value: '1'
                    }).appendTo('form');
                }
            });

            // Check if we have a recently uploaded logo stored
            let lastUploadedLogo = localStorage.getItem('lastUploadedLogo');
            let logoUpdateTime = localStorage.getItem('logoUpdateTime');

            // Only use the stored logo if it was updated recently (within the last minute)
            if (lastUploadedLogo && logoUpdateTime) {
                const currentTime = new Date().getTime();
                const timeDiff = currentTime - logoUpdateTime;

                // If logo was updated within the last minute, use it
                if (timeDiff < 60000) {
                    $('.sidebar img.rounded-circle').attr('src', lastUploadedLogo);

                    // Clear the stored logo after using it
                    setTimeout(function() {
                        localStorage.removeItem('lastUploadedLogo');
                        localStorage.removeItem('logoUpdateTime');
                    }, 5000);
                } else {
                    // Clear old data
                    localStorage.removeItem('lastUploadedLogo');
                    localStorage.removeItem('logoUpdateTime');
                }
            }
        });

        // Function to show toast notifications
        function showToast(message, type = 'success') {
            // Create toast container if it doesn't exist
            let toastContainer = document.querySelector('.toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '9999';
                document.body.appendChild(toastContainer);
            }

            // Create toast element
            const toastId = 'toast-' + Date.now();
            const toastElement = document.createElement('div');
            toastElement.id = toastId;
            toastElement.className = `toast align-items-center text-white bg-${type} border-0 shadow`;
            toastElement.setAttribute('role', 'alert');
            toastElement.setAttribute('aria-live', 'assertive');
            toastElement.setAttribute('aria-atomic', 'true');

            // Create toast content
            toastElement.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            `;

            // Add toast to container
            toastContainer.appendChild(toastElement);

            // Initialize and show toast
            const toast = new bootstrap.Toast(toastElement, {
                autohide: true,
                delay: 2000
            });
            toast.show();

            // If success message, refresh parent window after toast is shown
            if (type === 'success' && message.includes('successfully updated')) {
                setTimeout(function() {
                    // Instead of reloading the entire page, just refresh the logo if it was updated
                    if (localStorage.getItem('lastUploadedLogo')) {
                        $('.sidebar img.rounded-circle').attr('src', localStorage.getItem('lastUploadedLogo'));
                    } else {
                        // Only reload the page if necessary for other settings changes
                        window.location.reload();
                    }
                }, 2000); // Match the delay time with toast duration
            }

            // Remove toast after it's hidden
            toastElement.addEventListener('hidden.bs.toast', function() {
                toastElement.remove();
                // Remove container if empty
                if (toastContainer.children.length === 0) {
                    toastContainer.remove();
                }
            });
        }

        // Preview logo before upload
        $('#logo').on('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Create preview container if it doesn't exist
                    let previewContainer = $('.mb-3.border.p-3.rounded.text-center.bg-light');
                    if (previewContainer.length === 0) {
                        previewContainer = $('<div class="mb-3 border p-3 rounded text-center bg-light"></div>');
                        previewContainer.insertBefore($('#logo').parent());
                    }

                    // Update preview image
                    if (previewContainer.find('img').length === 0) {
                        previewContainer.html('<img src="' + e.target.result + '" alt="Logo Preview" class="img-thumbnail" style="max-width: 150px; max-height: 150px;"><p class="mt-2 mb-0 text-muted">New Logo Preview</p>');
                    } else {
                        previewContainer.find('img').attr('src', e.target.result);
                        previewContainer.find('p').text('New Logo Preview');
                    }

                    // Also update the sidebar logo for preview
                    $('.sidebar img.rounded-circle').attr('src', e.target.result);
                };
                reader.readAsDataURL(file);
            }
        });

        // Preview login background before upload
        $('#login_background').on('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Find the login background preview container
                    let previewContainer = $('#login_background').closest('.col-md-6').find('.mb-3.border.p-3.rounded.text-center.bg-light');
                    if (previewContainer.length === 0) {
                        previewContainer = $('<div class="mb-3 border p-3 rounded text-center bg-light"></div>');
                        previewContainer.insertBefore($('#login_background').parent());
                    }

                    // Update preview image
                    if (previewContainer.find('img').length === 0) {
                        previewContainer.html('<img src="' + e.target.result + '" alt="Login Background Preview" class="img-thumbnail" style="max-width: 300px; max-height: 200px;"><p class="mt-2 mb-0 text-muted">New Login Background Preview</p>');
                    } else {
                        previewContainer.find('img').attr('src', e.target.result);
                        previewContainer.find('p').text('New Login Background Preview');
                    }
                };
                reader.readAsDataURL(file);
            }
        });

        // Preview favicon before upload
        $('#favicon').on('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Find the favicon preview container
                    let previewContainer = $('#favicon').closest('.col-md-6').find('.mb-3.border.p-3.rounded.text-center.bg-light');
                    if (previewContainer.length === 0) {
                        previewContainer = $('<div class="mb-3 border p-3 rounded text-center bg-light"></div>');
                        previewContainer.insertBefore($('#favicon').parent());
                    }

                    // Update preview image
                    if (previewContainer.find('img').length === 0) {
                        previewContainer.html('<img src="' + e.target.result + '" alt="Favicon Preview" class="img-thumbnail" style="max-width: 64px; max-height: 64px;"><p class="mt-2 mb-0 text-muted">New Favicon Preview</p>');
                    } else {
                        previewContainer.find('img').attr('src', e.target.result);
                        previewContainer.find('p').text('New Favicon Preview');
                    }
                };
                reader.readAsDataURL(file);
            }
        });

        // Auto-activate templates tab if URL contains #templates
        $(document).ready(function() {
            if (window.location.hash === '#templates') {
                $('#templates-tab').tab('show');
            }
        });
    </script>
</body>
</html>