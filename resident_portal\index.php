<?php
// Initialize session
session_start();

// Include database and utility functions
include '../includes/config/database.php';
include '../includes/functions/utility.php';

// Get system settings
$barangay_name = "Barangay";
$barangay_address = "";
$contact_number = "";
$email = "";
$office_hours_weekday = "Monday to Friday: 8:00 AM - 5:00 PM";
$office_hours_weekend = "Saturday: 8:00 AM - 12:00 PM";
$barangay_description = "The Barangay Resident Portal provides online services to residents, making it easier to request documents and access barangay services from the comfort of your home.";
$facebook_url = "";
$twitter_url = "";
$instagram_url = "";
try {
    $query = "SELECT * FROM system_settings WHERE setting_name IN ('barangay_name', 'barangay_address', 'contact_number', 'email', 'office_hours_weekday', 'office_hours_weekend', 'barangay_description', 'facebook_url', 'twitter_url', 'instagram_url') LIMIT 10";
    $stmt = $conn->prepare($query);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        while ($row = $stmt->fetch()) {
            if ($row['setting_name'] == 'barangay_name') {
                $barangay_name = $row['setting_value'];
            } elseif ($row['setting_name'] == 'barangay_address') {
                $barangay_address = $row['setting_value'];
            } elseif ($row['setting_name'] == 'contact_number') {
                $contact_number = $row['setting_value'];
            } elseif ($row['setting_name'] == 'email') {
                $email = $row['setting_value'];
            } elseif ($row['setting_name'] == 'office_hours_weekday') {
                $office_hours_weekday = $row['setting_value'];
            } elseif ($row['setting_name'] == 'office_hours_weekend') {
                $office_hours_weekend = $row['setting_value'];
            } elseif ($row['setting_name'] == 'barangay_description') {
                $barangay_description = $row['setting_value'];
            } elseif ($row['setting_name'] == 'facebook_url') {
                $facebook_url = $row['setting_value'];
            } elseif ($row['setting_name'] == 'twitter_url') {
                $twitter_url = $row['setting_value'];
            } elseif ($row['setting_name'] == 'instagram_url') {
                $instagram_url = $row['setting_value'];
            }
        }
    }
} catch (PDOException $e) {
    // Default values if there's an error
}

// Get logo path using unified function
$logo_path = get_logo_path($conn, '../');

// Page title - check if barangay name already contains "Management System"
if (stripos($barangay_name, 'Management System') !== false) {
    $page_title = $barangay_name . " - Resident Portal";
} else {
    $page_title = $barangay_name . " Resident Portal";
}

// Check if user is logged in
$is_logged_in = isset($_SESSION['resident_id']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>

    <!-- Favicon -->
    <link rel="icon" href="<?php echo get_favicon_url($conn, '../'); ?>" type="image/png">

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        :root {
            --primary-color: #4e73df;
            --primary-dark: #3a56b7;
            --primary-light: rgba(78, 115, 223, 0.1);
            --secondary-color: #1cc88a;
            --secondary-dark: #169e6c;
            --secondary-light: rgba(28, 200, 138, 0.1);
            --dark-color: #2e384d;
            --light-color: #f8f9fc;
            --gray-color: #858796;
            --danger-color: #e74a3b;
            --warning-color: #f6c23e;
            --info-color: #36b9cc;
            --card-border-radius: 1rem;
            --btn-border-radius: 0.5rem;
            --box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            --box-shadow-hover: 0 1rem 3rem rgba(0, 0, 0, 0.15);
            --transition: all 0.3s ease;
            --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            --gradient-secondary: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
            --gradient-hero: linear-gradient(135deg, rgba(46, 56, 77, 0.95), rgba(78, 115, 223, 0.9));
        }

        body {
            background-color: var(--light-color);
            font-family: 'Poppins', sans-serif;
            color: #444;
            line-height: 1.7;
        }

        /* Navbar Styling */
        .navbar-container {
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
            width: 100%;
        }

        .navbar {
            padding: 0.75rem 0;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
        }

        .navbar-brand-icon {
            width: 36px;
            height: 36px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }

        .nav-item {
            margin: 0 0.25rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.25rem;
            font-weight: 500;
            margin: 0 0.25rem;
            line-height: 1;
        }

        .nav-icon {
            width: 28px;
            height: 28px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            transition: var(--transition);
        }

        .navbar-dark .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.85);
            font-weight: 500;
            transition: var(--transition);
        }

        .navbar-dark .navbar-nav .nav-link:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .navbar-dark .navbar-nav .nav-link:hover .nav-icon {
            background: rgba(255, 255, 255, 0.2);
        }

        .navbar-dark .navbar-nav .nav-link.active {
            background-color: rgba(255, 255, 255, 0.15);
        }

        .navbar-dark .navbar-nav .nav-link.active .nav-icon {
            background: rgba(255, 255, 255, 0.2);
        }

        .navbar-toggler {
            position: relative;
            z-index: 2;
            border: none;
            padding: 0.5rem;
        }

        .dropdown-menu {
            border-radius: 10px;
            border: none;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 0.5rem;
            margin-top: 0.5rem;
        }

        .dropdown-item {
            border-radius: 6px;
            padding: 0.5rem 1rem;
            transition: var(--transition);
        }

        .dropdown-item:hover {
            background-color: rgba(67, 97, 238, 0.1);
        }

        .dropdown-item.text-danger:hover {
            background-color: rgba(230, 57, 70, 0.1);
        }

        /* Hero Section */
        .hero-section {
            background: var(--gradient-hero), url('../assets/img/barangay_hero.jpg');
            background-size: cover;
            background-position: center;
            color: white;
            padding: 8rem 0 12rem; /* Increased bottom padding */
            margin-bottom: -8rem; /* Negative margin to create overlap with services section */
            position: relative;
            overflow: visible;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            clip-path: ellipse(100% 100% at 50% 0%);
        }

        .hero-particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .hero-section .container {
            position: relative;
            z-index: 2;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 50px;
            padding: 0.5rem 1.25rem;
            font-weight: 500;
            font-size: 0.9rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            line-height: 1.2;
        }

        .text-gradient {
            background: linear-gradient(to right, #fff, #a5b4fc);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .hero-subtitle {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            max-width: 600px;
            line-height: 1.6;
        }

        .hero-btn-primary {
            background: var(--gradient-primary);
            border: none;
            box-shadow: 0 10px 20px rgba(78, 115, 223, 0.3);
            transition: all 0.3s ease;
        }

        .hero-btn-primary:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(78, 115, 223, 0.4);
        }

        .hero-btn-secondary {
            border: 2px solid rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

        .hero-btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-5px);
        }

        .hero-stats {
            margin-top: 2rem;
        }

        .hero-stat-item {
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border-radius: 12px;
            padding: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .hero-stat-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-5px);
        }

        .hero-stat-icon {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.5rem;
        }

        .hero-stat-content h3 {
            font-size: 1rem;
            font-weight: 600;
            margin: 0;
        }

        .hero-stat-content p {
            font-size: 0.85rem;
            margin: 0;
            opacity: 0.8;
        }

        .hero-image-container {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .hero-image {
            max-width: 80%;
            height: auto;
            position: relative;
            z-index: 2;
            filter: drop-shadow(0 20px 30px rgba(0, 0, 0, 0.2));
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            padding: 20px;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .hero-shape-1 {
            position: absolute;
            top: -20px;
            right: 20px;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            z-index: 1;
            animation: float 6s ease-in-out infinite;
        }

        .hero-shape-2 {
            position: absolute;
            bottom: 0;
            left: 20px;
            width: 150px;
            height: 150px;
            background: rgba(28, 200, 138, 0.2);
            border-radius: 50%;
            z-index: 1;
            animation: float 8s ease-in-out infinite reverse;
        }

        @keyframes float {
            0% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-20px);
            }
            100% {
                transform: translateY(0);
            }
        }

        /* Buttons */
        .btn {
            border-radius: var(--btn-border-radius);
            padding: 0.6rem 1.75rem;
            font-weight: 500;
            letter-spacing: 0.5px;
            transition: var(--transition);
            text-transform: uppercase;
            font-size: 0.85rem;
        }

        .btn-lg {
            padding: 0.8rem 2.5rem;
            font-size: 0.95rem;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            box-shadow: 0 4px 10px rgba(78, 115, 223, 0.3);
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(78, 115, 223, 0.4);
        }

        .btn-success {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            box-shadow: 0 4px 10px rgba(28, 200, 138, 0.3);
        }

        .btn-success:hover {
            background-color: #169e6c;
            border-color: #169e6c;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(28, 200, 138, 0.4);
        }

        .btn-outline-light {
            border-width: 2px;
            font-weight: 500;
        }

        .btn-outline-light:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateY(-3px);
        }

        /* Section Styling */
        section {
            padding: 5rem 0;
        }

        .section-title {
            position: relative;
            display: inline-block;
            margin-bottom: 3rem;
            font-weight: 700;
            color: var(--dark-color);
            font-size: 2rem;
        }

        .section-title::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: -15px;
            width: 70px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 2px;
        }

        .section-subtitle {
            color: var(--gray-color);
            font-size: 1.1rem;
            max-width: 700px;
            margin-bottom: 3rem;
        }

        /* Services Section */
        .services-section {
            padding: 10rem 0 6rem;
            position: relative;
            overflow: visible;
            background-color: #ffffff;
            margin-top: 0;
            z-index: 2;
            box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.05);
        }

        .section-badge {
            display: inline-block;
            background: var(--primary-light);
            border-radius: 50px;
            padding: 0.5rem 1.25rem;
            font-weight: 500;
            font-size: 0.9rem;
            color: var(--primary-color);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(78, 115, 223, 0.1);
        }

        /* Service Cards */
        .service-card-wrapper {
            perspective: 1000px;
            height: 100%;
        }

        .service-card {
            height: 100%;
            min-height: 450px;
            border: none;
            border-radius: var(--card-border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
            position: relative;
            transition: var(--transition);
            transform-style: preserve-3d;
            background-color: transparent;
        }

        .service-card:hover {
            box-shadow: var(--box-shadow-hover);
        }

        .service-card-inner {
            position: relative;
            width: 100%;
            height: 100%;
            text-align: center;
            transition: transform 0.8s;
            transform-style: preserve-3d;
        }

        .service-card.flipped .service-card-inner {
            transform: rotateY(180deg);
        }

        .service-card-front, .service-card-back {
            position: absolute;
            width: 100%;
            height: 100%;
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
            display: flex;
            flex-direction: column;
            padding: 2rem;
            background-color: white;
            border-radius: var(--card-border-radius);
        }

        .service-card-back {
            transform: rotateY(180deg);
            background: var(--gradient-primary);
            color: white;
        }

        .service-icon-wrapper {
            margin-bottom: 1.5rem;
        }

        .service-icon {
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            border-radius: 20px;
            background: linear-gradient(135deg, rgba(78, 115, 223, 0.1), rgba(28, 200, 138, 0.1));
            color: var(--primary-color);
            font-size: 2rem;
            transition: var(--transition);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
        }

        .service-card:hover .service-icon {
            transform: rotateY(180deg);
        }

        .service-title {
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--dark-color);
            font-size: 1.5rem;
        }

        .service-text {
            color: var(--gray-color);
            margin-bottom: 1.5rem;
            font-size: 0.95rem;
        }

        .service-features {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }

        .service-feature {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: var(--gray-color);
        }

        .service-feature i {
            color: var(--secondary-color);
        }

        .service-action {
            margin-top: auto;
        }

        .service-details {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin: 1.5rem 0;
            text-align: left;
        }

        .service-detail-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }

        .service-detail-item i {
            font-size: 1.25rem;
            margin-top: 0.25rem;
        }

        .service-detail-item h5 {
            font-size: 0.9rem;
            font-weight: 600;
            margin: 0;
            margin-bottom: 0.25rem;
        }

        .service-detail-item p {
            font-size: 0.85rem;
            margin: 0;
            opacity: 0.9;
        }

        .service-list {
            list-style: none;
            padding: 0;
            margin: 1.5rem 0;
            text-align: left;
        }

        .service-list li {
            margin-bottom: 0.75rem;
            font-size: 0.9rem;
        }

        .service-card-back .btn-primary {
            background: white;
            color: var(--primary-color);
            border: none;
        }

        .service-card-back .btn-primary:hover {
            background: rgba(255, 255, 255, 0.9);
            transform: translateY(-3px);
        }

        .service-card-back .btn-outline-light {
            border-color: rgba(255, 255, 255, 0.3);
        }

        .service-card-back .btn-outline-light:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        /* How It Works Section */
        .bg-light {
            background-color: #f1f5fe !important;
        }

        .step-box {
            background-color: white;
            border-radius: var(--card-border-radius);
            padding: 2.5rem 1.5rem;
            text-align: center;
            box-shadow: var(--box-shadow);
            transition: var(--transition);
            height: 100%;
            position: relative;
            z-index: 1;
        }

        .step-box:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .step-number {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .step-box h4 {
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--dark-color);
        }

        .step-box p {
            color: var(--gray-color);
        }

        /* Announcements Section */
        .announcement-card {
            border: none;
            border-radius: var(--card-border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
            transition: var(--transition);
            height: 100%;
        }

        .announcement-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .announcement-card .card-body {
            padding: 2rem;
        }

        .announcement-card .card-title {
            font-weight: 600;
            color: var(--dark-color);
        }

        .announcement-date {
            color: var(--gray-color);
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .announcement-card .card-text {
            color: var(--gray-color);
            margin-bottom: 1.5rem;
        }

        .announcement-card .card-footer {
            background-color: transparent;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            padding: 1rem 2rem;
        }

        /* Featured Announcement Styling */
        .featured-announcement {
            position: relative;
            border: 2px solid #ffc107 !important;
            box-shadow: 0 8px 25px rgba(255, 193, 7, 0.2) !important;
        }

        .featured-announcement:hover {
            box-shadow: 0 15px 35px rgba(255, 193, 7, 0.3) !important;
        }

        .featured-badge {
            position: absolute;
            top: -1px;
            right: -1px;
            z-index: 10;
        }

        .featured-badge .badge {
            border-radius: 0 var(--card-border-radius) 0 15px;
            padding: 0.5rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 600;
        }

        /* Contact Section */
        .contact-card {
            border: none;
            border-radius: var(--card-border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
            height: 100%;
        }

        .contact-card .card-body {
            padding: 2rem;
        }

        .contact-card .card-title {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 1.5rem;
        }

        .contact-info-item {
            margin-bottom: 1.5rem;
        }

        .contact-info-item h5 {
            font-weight: 600;
            color: var(--dark-color);
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .contact-info-item h5 i {
            color: var(--primary-color);
            margin-right: 0.75rem;
            font-size: 1.25rem;
        }

        .contact-info-item p {
            color: var(--gray-color);
            margin-left: 2rem;
        }

        .form-control {
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
            border: 1px solid #e1e5eb;
        }

        .form-control:focus {
            box-shadow: none;
            border-color: var(--primary-color);
        }

        /* Footer */
        footer {
            background-color: var(--dark-color);
            color: white;
            padding: 4rem 0 2rem;
            margin-top: 0;
        }

        footer h5 {
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: white;
        }

        footer p {
            color: rgba(255, 255, 255, 0.7);
        }

        .footer-links {
            list-style: none;
            padding: 0;
        }

        .footer-links li {
            margin-bottom: 0.75rem;
        }

        .footer-links a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: var(--transition);
        }

        .footer-links a:hover {
            color: var(--secondary-color);
            padding-left: 5px;
        }

        .social-icons {
            margin-top: 1.5rem;
        }

        .social-icons a {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            margin-right: 0.75rem;
            transition: var(--transition);
        }

        .social-icons a:hover {
            background-color: var(--primary-color);
            transform: translateY(-3px);
        }

        footer hr {
            background-color: rgba(255, 255, 255, 0.1);
            margin: 2rem 0;
        }

        .copyright {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.9rem;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.8s;
        }

        .animate-fade-in-down {
            animation: fadeInDown 0.8s;
        }

        /* Responsive Adjustments */
        @media (max-width: 992px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-section {
                padding: 5rem 0 3rem;
            }

            .section-title {
                font-size: 1.75rem;
            }
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            section {
                padding: 3rem 0;
            }

            .step-box {
                margin-bottom: 2rem;
            }
        }

        /* Header Area - New Styles */
        .header-area {
            position: relative;
            z-index: 1000;
        }

        /* Top Header */
        .top-header {
            background-color: var(--dark-color);
            padding: 0.5rem 0;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.85rem;
        }

        .top-header-info ul {
            display: flex;
            flex-wrap: wrap;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .top-header-info li {
            margin-right: 1.5rem;
            display: flex;
            align-items: center;
        }

        .top-header-info li i {
            margin-right: 0.5rem;
            color: var(--secondary-color);
        }

        .top-header-social {
            display: flex;
            justify-content: flex-end;
            gap: 0.75rem;
        }

        .top-header-social a {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
            transition: var(--transition);
        }

        .top-header-social a:hover {
            background-color: var(--secondary-color);
            color: white;
            transform: translateY(-3px);
        }

        /* Main Navbar - Updated Styles */
        .navbar-container {
            background-color: white;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .navbar-brand-wrapper {
            display: flex;
            align-items: center;
        }

        .navbar-brand-icon {
            background: var(--gradient-primary);
            color: white;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(78, 115, 223, 0.3);
        }

        .navbar-brand-text {
            display: flex;
            flex-direction: column;
        }

        .navbar-brand-title {
            font-weight: 700;
            font-size: 1.25rem;
            color: var(--dark-color);
            line-height: 1.2;
        }

        .navbar-brand-subtitle {
            font-size: 0.75rem;
            color: var(--gray-color);
            font-weight: 500;
        }

        .nav-link {
            color: var(--dark-color) !important;
            font-weight: 600;
        }

        .nav-link:hover, .nav-link.active {
            background-color: var(--primary-light);
            color: var(--primary-color) !important;
        }

        .nav-icon {
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-radius: 10px;
        }

        .nav-link:hover .nav-icon {
            background: var(--gradient-primary);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(78, 115, 223, 0.2);
        }

        .badge {
            padding: 0.35em 0.65em;
            font-weight: 600;
        }

        /* Footer Styles */
        .footer {
            position: relative;
            margin-top: 3rem;
        }

        .footer-top {
            background: var(--gradient-primary);
            color: white;
            padding: 5rem 0 3rem;
            position: relative;
            border-top-left-radius: 30px;
            border-top-right-radius: 30px;
            margin-top: -30px;
            box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.1);
        }

        .footer-logo {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .footer-logo-icon {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-right: 1rem;
        }

        .footer-logo-text h4 {
            margin: 0;
            font-weight: 700;
            font-size: 1.5rem;
        }

        .footer-logo-text p {
            margin: 0;
            font-size: 0.85rem;
            opacity: 0.8;
        }

        .footer-description {
            margin-bottom: 1.5rem;
            line-height: 1.6;
            opacity: 0.9;
        }

        .footer-cta {
            margin-bottom: 2rem;
        }

        .footer-widget {
            margin-bottom: 2rem;
        }

        .footer-widget-title {
            font-weight: 700;
            margin-bottom: 1.5rem;
            font-size: 1.25rem;
            position: relative;
            padding-bottom: 0.75rem;
        }

        .footer-widget-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .footer-links {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .footer-links li {
            margin-bottom: 0.75rem;
        }

        .footer-links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: var(--transition);
            display: flex;
            align-items: center;
        }

        .footer-links a:hover {
            color: white;
            transform: translateX(5px);
        }

        .footer-contact-info {
            margin-bottom: 1.5rem;
        }

        .footer-contact-item {
            display: flex;
            margin-bottom: 1rem;
        }

        .footer-contact-item i {
            width: 30px;
            height: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 0.9rem;
        }

        .footer-contact-item p {
            margin: 0;
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .footer-social {
            display: flex;
            gap: 0.75rem;
        }

        .footer-social-icon {
            width: 36px;
            height: 36px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            transition: var(--transition);
        }

        .footer-social-icon:hover {
            background: white;
            color: var(--primary-color);
            transform: translateY(-5px);
        }

        .footer-bottom {
            background: var(--dark-color);
            padding: 1.5rem 0;
            color: rgba(255, 255, 255, 0.7);
        }

        .copyright {
            margin: 0;
            font-size: 0.9rem;
        }

        .footer-bottom-links {
            display: flex;
            justify-content: flex-end;
            list-style: none;
            margin: 0;
            padding: 0;
            gap: 1.5rem;
        }

        .footer-bottom-links a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            font-size: 0.9rem;
            transition: var(--transition);
        }

        .footer-bottom-links a:hover {
            color: white;
        }

        /* Announcement Modal Styles */
        .announcement-type {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .announcement-type.general {
            background-color: rgba(67, 97, 238, 0.1);
            color: var(--primary-color);
        }

        .announcement-type.event {
            background-color: rgba(114, 9, 183, 0.1);
            color: var(--secondary-color);
        }

        .announcement-type.emergency {
            background-color: rgba(231, 29, 54, 0.1);
            color: var(--danger-color);
        }

        .announcement-type.meeting {
            background-color: rgba(58, 134, 255, 0.1);
            color: var(--info-color);
        }

        .announcement-type.community {
            background-color: rgba(255, 159, 28, 0.1);
            color: var(--warning-color);
        }

        .announcement-meta {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            color: #6c757d;
            font-size: 0.9rem;
            gap: 1rem;
        }

        .announcement-meta-item {
            display: flex;
            align-items: center;
        }

        .announcement-meta-item i {
            margin-right: 0.5rem;
        }

        @media (max-width: 767px) {
            .footer-bottom-links {
                justify-content: center;
                margin-top: 1rem;
            }

            .copyright {
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header/Navbar -->
    <header class="header-area">
        <!-- Top Header -->
        <div class="top-header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="top-header-info">
                            <ul>
                                <li><i class="fas fa-map-marker-alt"></i> <?php echo $barangay_address ? htmlspecialchars($barangay_address) : 'Barangay Hall Address'; ?></li>
                                <li><i class="fas fa-envelope"></i> <?php echo $email ? htmlspecialchars($email) : '<EMAIL>'; ?></li>
                                <li><i class="fas fa-phone"></i> <?php echo $contact_number ? htmlspecialchars($contact_number) : 'Contact Number'; ?></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="top-header-social">
                            <?php if (!empty($facebook_url)): ?>
                            <a href="<?php echo htmlspecialchars($facebook_url); ?>" target="_blank" rel="noopener noreferrer" title="Follow us on Facebook"><i class="fab fa-facebook-f"></i></a>
                            <?php endif; ?>
                            <?php if (!empty($twitter_url)): ?>
                            <a href="<?php echo htmlspecialchars($twitter_url); ?>" target="_blank" rel="noopener noreferrer" title="Follow us on Twitter"><i class="fab fa-twitter"></i></a>
                            <?php endif; ?>
                            <?php if (!empty($instagram_url)): ?>
                            <a href="<?php echo htmlspecialchars($instagram_url); ?>" target="_blank" rel="noopener noreferrer" title="Follow us on Instagram"><i class="fab fa-instagram"></i></a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Navbar -->
        <div class="navbar-container">
            <nav class="navbar navbar-expand-lg">
                <div class="container">
                    <a class="navbar-brand" href="index.php">
                        <div class="navbar-brand-wrapper">
                            <div class="navbar-brand-icon">
                                <i class="fas fa-landmark"></i>
                            </div>
                            <div class="navbar-brand-text">
                                <span class="navbar-brand-title"><?php echo $barangay_name; ?></span>
                                <span class="navbar-brand-subtitle"><?php echo $barangay_name; ?> Resident Portal</span>
                            </div>
                        </div>
                    </a>
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                        <span class="navbar-toggler-icon"><i class="fas fa-bars"></i></span>
                    </button>
                    <div class="collapse navbar-collapse" id="navbarNav">
                        <ul class="navbar-nav ms-auto">
                            <?php if ($is_logged_in): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="dashboard.php">
                                    <div class="nav-icon">
                                        <i class="fas fa-tachometer-alt"></i>
                                    </div>
                                    Dashboard
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="request_document.php">
                                    <div class="nav-icon">
                                        <i class="fas fa-file-alt"></i>
                                    </div>
                                    <span>Request Document</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="my_requests.php">
                                    <div class="nav-icon">
                                        <i class="fas fa-clipboard-list"></i>
                                    </div>
                                    <span>My Requests</span>
                                    <?php
                                    // Get pending requests count
                                    $pending_count = 0;
                                    try {
                                        $query = "SELECT COUNT(*) FROM document_requests WHERE resident_id = :resident_id AND status = 'Pending'";
                                        $stmt = $conn->prepare($query);
                                        $stmt->bindParam(':resident_id', $_SESSION['resident_id']);
                                        $stmt->execute();
                                        $pending_count = $stmt->fetchColumn();
                                    } catch (PDOException $e) {
                                        // Ignore errors
                                    }

                                    if ($pending_count > 0):
                                    ?>
                                    <span class="badge rounded-pill bg-danger ms-1"><?php echo $pending_count; ?></span>
                                    <?php endif; ?>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="notifications.php">
                                    <div class="nav-icon">
                                        <i class="fas fa-bell"></i>
                                    </div>
                                    Notifications
                                    <?php
                                    // Get unread notification count
                                    $unread_count = 0;
                                    try {
                                        $query = "SELECT COUNT(*) FROM resident_notifications WHERE resident_id = :resident_id AND is_read = 0";
                                        $stmt = $conn->prepare($query);
                                        $stmt->bindParam(':resident_id', $_SESSION['resident_id']);
                                        $stmt->execute();
                                        $unread_count = $stmt->fetchColumn();
                                    } catch (PDOException $e) {
                                        // Ignore errors
                                    }

                                    if ($unread_count > 0):
                                    ?>
                                    <span class="badge rounded-pill bg-danger ms-1"><?php echo $unread_count; ?></span>
                                    <?php endif; ?>
                                </a>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <div class="nav-icon">
                                        <i class="fas fa-user-circle"></i>
                                    </div>
                                    Profile
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                    <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-circle me-2"></i> My Profile</a></li>
                                    <li><a class="dropdown-item" href="profile.php#password-tab"><i class="fas fa-key me-2"></i> Change Password</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                                </ul>
                            </li>
                            <?php else: ?>
                            <li class="nav-item">
                                <a class="nav-link" href="login.php">
                                    <div class="nav-icon">
                                        <i class="fas fa-sign-in-alt"></i>
                                    </div>
                                    Login
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="register.php">
                                    <div class="nav-icon">
                                        <i class="fas fa-user-plus"></i>
                                    </div>
                                    Register
                                </a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="hero-particles" id="particles-js"></div>
        <div class="container position-relative">
            <div class="row align-items-center">
                <div class="col-lg-7">
                    <div class="hero-content text-lg-start text-center">
                        <div class="hero-badge animate-fade-in-down mb-3">
                            <span><i class="fas fa-award me-2"></i>Official Barangay Portal</span>
                        </div>
                        <h1 class="hero-title display-4 fw-bold"><?php echo $barangay_name; ?> <span class="text-gradient">Online Services</span></h1>
                        <p class="hero-subtitle lead">Request documents, certificates, and access barangay services online anytime, anywhere. Skip the lines and save time with our digital platform.</p>

                        <?php if (!$is_logged_in): ?>
                        <!-- Login/Register buttons removed from hero section as requested -->
                        <div class="hero-stats mt-5 d-flex flex-wrap justify-content-lg-start justify-content-center gap-4">
                            <div class="hero-stat-item animate-fade-in-up" style="animation-delay: 0.9s;">
                                <div class="hero-stat-icon"><i class="fas fa-file-alt"></i></div>
                                <div class="hero-stat-content">
                                    <h3>Fast Processing</h3>
                                    <p>Get documents in 1-3 days</p>
                                </div>
                            </div>
                            <div class="hero-stat-item animate-fade-in-up" style="animation-delay: 1.1s;">
                                <div class="hero-stat-icon"><i class="fas fa-clock"></i></div>
                                <div class="hero-stat-content">
                                    <h3>24/7 Access</h3>
                                    <p>Request anytime, anywhere</p>
                                </div>
                            </div>
                        </div>
                        <?php else: ?>
                        <div class="d-flex flex-wrap gap-3 mt-5 justify-content-lg-start justify-content-center">
                            <a href="dashboard.php" class="btn btn-primary btn-lg animate-fade-in-up hero-btn-primary" style="animation-delay: 0.5s;">
                                <i class="fas fa-tachometer-alt me-2"></i> Go to Dashboard
                            </a>
                            <a href="request_document.php" class="btn btn-success btn-lg animate-fade-in-up" style="animation-delay: 0.7s;">
                                <i class="fas fa-file-alt me-2"></i> Request Document
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-lg-5 d-none d-lg-block">
                    <div class="hero-image-container animate-fade-in-up" style="animation-delay: 0.3s;">
                        <img src="<?php echo $logo_path; ?>" alt="Barangay Logo" class="hero-image">
                        <div class="hero-shape-1"></div>
                        <div class="hero-shape-2"></div>
                    </div>
                </div>
            </div>
        </div>

    </section>

    <!-- Services Section -->
    <section id="services" class="services-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-7 text-center">
                    <div class="section-badge mb-3">
                        <span><i class="fas fa-star me-2"></i>Our Services</span>
                    </div>
                    <h2 class="section-title">Digital Barangay Services</h2>
                    <p class="section-subtitle">Access a variety of barangay services online through our resident portal. Save time and avoid long queues with our streamlined digital process.</p>
                </div>
            </div>

            <div class="row mt-5">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card-wrapper animate-fade-in-up" style="animation-delay: 0.1s;">
                        <div class="service-card">
                            <div class="service-card-inner">
                                <div class="service-card-front">
                                    <div class="service-icon-wrapper">
                                        <div class="service-icon">
                                            <i class="fas fa-file-alt"></i>
                                        </div>
                                    </div>
                                    <h4 class="service-title">Barangay Clearance</h4>
                                    <p class="service-text">Request a Barangay Clearance for employment, scholarship, bank, or legal requirements.</p>
                                    <div class="service-features">
                                        <div class="service-feature">
                                            <i class="fas fa-check-circle"></i>
                                            <span>Fast Processing</span>
                                        </div>
                                        <div class="service-feature">
                                            <i class="fas fa-check-circle"></i>
                                            <span>Official Document</span>
                                        </div>
                                    </div>
                                    <div class="service-action">
                                        <button class="btn btn-light btn-sm service-flip-btn">
                                            <i class="fas fa-info-circle me-1"></i> More Details
                                        </button>
                                    </div>
                                </div>
                                <div class="service-card-back">
                                    <h4>Barangay Clearance</h4>
                                    <div class="service-details">
                                        <div class="service-detail-item">
                                            <i class="fas fa-clock"></i>
                                            <div>
                                                <h5>Processing Time</h5>
                                                <p>1-2 business days</p>
                                            </div>
                                        </div>
                                        <div class="service-detail-item">
                                            <i class="fas fa-tag"></i>
                                            <div>
                                                <h5>Fee</h5>
                                                <p>₱50.00</p>
                                            </div>
                                        </div>
                                        <div class="service-detail-item">
                                            <i class="fas fa-file-alt"></i>
                                            <div>
                                                <h5>Requirements</h5>
                                                <p>Valid ID, Proof of Residency</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="service-action mt-auto">
                                        <?php if ($is_logged_in): ?>
                                        <a href="request_document.php?type=clearance" class="btn btn-primary">
                                            <i class="fas fa-arrow-right me-2"></i>Request Now
                                        </a>
                                        <?php else: ?>
                                        <a href="login.php" class="btn btn-primary">
                                            <i class="fas fa-lock me-2"></i>Login to Request
                                        </a>
                                        <?php endif; ?>
                                        <button class="btn btn-outline-light btn-sm mt-2 service-flip-btn">
                                            <i class="fas fa-arrow-left me-1"></i> Back
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card-wrapper animate-fade-in-up" style="animation-delay: 0.2s;">
                        <div class="service-card">
                            <div class="service-card-inner">
                                <div class="service-card-front">
                                    <div class="service-icon-wrapper">
                                        <div class="service-icon" style="background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.1)); color: #e74c3c;">
                                            <i class="fas fa-certificate"></i>
                                        </div>
                                    </div>
                                    <h4 class="service-title">Certificates</h4>
                                    <p class="service-text">Request certificates like Residency, Indigency, Good Standing, and No Pending Case.</p>
                                    <div class="service-features">
                                        <div class="service-feature">
                                            <i class="fas fa-check-circle"></i>
                                            <span>Multiple Types</span>
                                        </div>
                                        <div class="service-feature">
                                            <i class="fas fa-check-circle"></i>
                                            <span>Digital Request</span>
                                        </div>
                                    </div>
                                    <div class="service-action">
                                        <button class="btn btn-light btn-sm service-flip-btn">
                                            <i class="fas fa-info-circle me-1"></i> More Details
                                        </button>
                                    </div>
                                </div>
                                <div class="service-card-back">
                                    <h4>Available Certificates</h4>
                                    <ul class="service-list">
                                        <li><i class="fas fa-check me-2"></i> Certificate of Residency</li>
                                        <li><i class="fas fa-check me-2"></i> Certificate of Indigency</li>
                                        <li><i class="fas fa-check me-2"></i> Certificate of Good Standing</li>
                                        <li><i class="fas fa-check me-2"></i> Certificate of No Pending Case</li>
                                    </ul>
                                    <div class="service-detail-item">
                                        <i class="fas fa-clock"></i>
                                        <div>
                                            <h5>Processing Time</h5>
                                            <p>1-3 business days</p>
                                        </div>
                                    </div>
                                    <div class="service-action mt-auto">
                                        <?php if ($is_logged_in): ?>
                                        <a href="request_document.php?type=certificate" class="btn btn-primary">
                                            <i class="fas fa-arrow-right me-2"></i>Request Now
                                        </a>
                                        <?php else: ?>
                                        <a href="login.php" class="btn btn-primary">
                                            <i class="fas fa-lock me-2"></i>Login to Request
                                        </a>
                                        <?php endif; ?>
                                        <button class="btn btn-outline-light btn-sm mt-2 service-flip-btn">
                                            <i class="fas fa-arrow-left me-1"></i> Back
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card-wrapper animate-fade-in-up" style="animation-delay: 0.3s;">
                        <div class="service-card">
                            <div class="service-card-inner">
                                <div class="service-card-front">
                                    <div class="service-icon-wrapper">
                                        <div class="service-icon" style="background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(41, 128, 185, 0.1)); color: #3498db;">
                                            <i class="fas fa-id-card"></i>
                                        </div>
                                    </div>
                                    <h4 class="service-title">Barangay ID</h4>
                                    <p class="service-text">Apply for a Barangay ID card which can be used as a valid government-issued ID.</p>
                                    <div class="service-features">
                                        <div class="service-feature">
                                            <i class="fas fa-check-circle"></i>
                                            <span>Official ID</span>
                                        </div>
                                        <div class="service-feature">
                                            <i class="fas fa-check-circle"></i>
                                            <span>Valid Identification</span>
                                        </div>
                                    </div>
                                    <div class="service-action">
                                        <button class="btn btn-light btn-sm service-flip-btn">
                                            <i class="fas fa-info-circle me-1"></i> More Details
                                        </button>
                                    </div>
                                </div>
                                <div class="service-card-back">
                                    <h4>Barangay ID</h4>
                                    <div class="service-details">
                                        <div class="service-detail-item">
                                            <i class="fas fa-clock"></i>
                                            <div>
                                                <h5>Processing Time</h5>
                                                <p>3-5 business days</p>
                                            </div>
                                        </div>
                                        <div class="service-detail-item">
                                            <i class="fas fa-tag"></i>
                                            <div>
                                                <h5>Fee</h5>
                                                <p>₱100.00</p>
                                            </div>
                                        </div>
                                        <div class="service-detail-item">
                                            <i class="fas fa-file-alt"></i>
                                            <div>
                                                <h5>Requirements</h5>
                                                <p>2x2 ID Picture, Proof of Residency</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="service-action mt-auto">
                                        <?php if ($is_logged_in): ?>
                                        <a href="request_document.php?type=id" class="btn btn-primary">
                                            <i class="fas fa-arrow-right me-2"></i>Request Now
                                        </a>
                                        <?php else: ?>
                                        <a href="login.php" class="btn btn-primary">
                                            <i class="fas fa-lock me-2"></i>Login to Request
                                        </a>
                                        <?php endif; ?>
                                        <button class="btn btn-outline-light btn-sm mt-2 service-flip-btn">
                                            <i class="fas fa-arrow-left me-1"></i> Back
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <?php if ($is_logged_in): ?>
                <a href="request_document.php" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-th-list me-2"></i>View All Services
                </a>
                <?php else: ?>
                <a href="register.php" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-user-plus me-2"></i>Register to Access Services
                </a>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section id="how-it-works" class="bg-light">
        <div class="container">
            <h2 class="section-title">How It Works</h2>
            <p class="section-subtitle">Our online portal makes it easy to request barangay documents and services in just a few simple steps.</p>

            <div class="row mt-4">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="step-box animate-fade-in-up" style="animation-delay: 0.1s;">
                        <div class="step-number">1</div>
                        <h4>Create an Account</h4>
                        <p>Register with your personal details to create a resident account. Ensure your information matches your barangay records.</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="step-box animate-fade-in-up" style="animation-delay: 0.2s;">
                        <div class="step-number">2</div>
                        <h4>Verify Your Identity</h4>
                        <p>Verify your email address and complete any required verification steps to activate your account.</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="step-box animate-fade-in-up" style="animation-delay: 0.3s;">
                        <div class="step-number">3</div>
                        <h4>Request Documents</h4>
                        <p>Select the document you need, provide the required information, and submit your request online.</p>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="step-box animate-fade-in-up" style="animation-delay: 0.4s;">
                        <div class="step-number">4</div>
                        <h4>Pick Up Your Document</h4>
                        <p>Visit the barangay hall to pay any applicable fees and collect your document when it's ready.</p>
                    </div>
                </div>
            </div>

            <div class="text-center mt-5">
                <?php if (!$is_logged_in): ?>
                <a href="register.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-user-plus me-2"></i>Register Now
                </a>
                <?php else: ?>
                <a href="request_document.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-file-alt me-2"></i>Request a Document
                </a>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Public Information Section -->
    <section id="announcements">
        <div class="container">
            <h2 class="section-title">📣 Latest Public Information</h2>
            <p class="section-subtitle">Stay updated with the latest announcements, ordinances, executive orders, and important information from your barangay.</p>

            <?php
            // Get latest public information (announcements, ordinances, executive orders)
            $announcements = [];
            try {
                $query = "SELECT *,
                         CASE
                            WHEN document_type = 'Ordinance' THEN '⚖️'
                            WHEN document_type = 'Executive Order' THEN '📜'
                            ELSE '📣'
                         END as doc_icon
                         FROM announcements
                         WHERE status = 'Active' AND is_featured = 1
                         ORDER BY date_posted DESC
                         LIMIT 6";
                $stmt = $conn->prepare($query);
                $stmt->execute();
                $announcements = $stmt->fetchAll(PDO::FETCH_ASSOC);
            } catch (PDOException $e) {
                // Handle error
                error_log("Error fetching public information: " . $e->getMessage());
            }

            if (!empty($announcements)):
            ?>
            <div class="row">
                <?php
                $delay = 0.1;
                foreach ($announcements as $announcement):
                ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="announcement-card animate-fade-in-up<?php echo $announcement['is_featured'] ? ' featured-announcement' : ''; ?>" style="animation-delay: <?php echo $delay; ?>s;">
                        <?php if ($announcement['is_featured']): ?>
                        <div class="featured-badge">
                            <span class="badge bg-warning text-dark">⭐ Featured</span>
                        </div>
                        <?php endif; ?>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <span class="badge bg-<?php
                                    echo ($announcement['document_type'] == 'Ordinance') ? 'success' :
                                         (($announcement['document_type'] == 'Executive Order') ? 'warning' : 'info');
                                ?>">
                                    <?php echo $announcement['doc_icon'] . ' ' . htmlspecialchars($announcement['document_type'] ?? 'Announcement'); ?>
                                </span>
                                <?php if (!empty($announcement['document_number'])): ?>
                                    <small class="text-muted">No. <?php echo htmlspecialchars($announcement['document_number']); ?></small>
                                <?php endif; ?>
                            </div>
                            <h5 class="card-title"><?php echo htmlspecialchars($announcement['title']); ?></h5>
                            <p class="announcement-date">
                                <i class="far fa-calendar-alt me-1"></i>
                                <?php echo date('F d, Y', strtotime($announcement['date_posted'])); ?>
                            </p>
                            <p class="card-text"><?php echo nl2br(htmlspecialchars(substr($announcement['content'], 0, 150) . (strlen($announcement['content']) > 150 ? '...' : ''))); ?></p>
                        </div>
                        <div class="card-footer">
                            <button type="button" class="btn btn-sm btn-outline-primary view-announcement-btn"
                                    data-bs-toggle="modal"
                                    data-bs-target="#viewAnnouncementModal"
                                    data-id="<?php echo $announcement['announcement_id']; ?>"
                                    data-title="<?php echo htmlspecialchars($announcement['title']); ?>"
                                    data-type="<?php echo htmlspecialchars($announcement['announcement_type'] ?? 'General'); ?>"
                                    data-date="<?php echo date('F d, Y', strtotime($announcement['date_posted'])); ?>"
                                    data-start-date="<?php echo !empty($announcement['start_date']) ? date('F d, Y', strtotime($announcement['start_date'])) : ''; ?>"
                                    data-end-date="<?php echo !empty($announcement['end_date']) ? date('F d, Y', strtotime($announcement['end_date'])) : ''; ?>"
                                    data-venue="<?php echo htmlspecialchars($announcement['venue'] ?? ''); ?>"
                                    data-content="<?php echo htmlspecialchars($announcement['content']); ?>"
                                    data-audience="<?php echo htmlspecialchars($announcement['target_audience'] ?? ''); ?>"
                                    data-image="<?php echo htmlspecialchars($announcement['image'] ?? ''); ?>">
                                <i class="fas fa-arrow-right me-1"></i> Read More
                            </button>
                        </div>
                    </div>
                </div>
                <?php
                $delay += 0.1;
                endforeach;
                ?>
            </div>
            <?php else: ?>
            <div class="alert alert-info animate-fade-in-up">
                <i class="fas fa-info-circle me-2"></i> No announcements available at this time.
            </div>
            <?php endif; ?>

            <div class="text-center mt-4">
                <a href="all_announcements.php" class="btn btn-outline-primary">
                    <i class="fas fa-bullhorn me-2"></i>View All Announcements
                </a>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="bg-light">
        <div class="container">
            <h2 class="section-title">Contact Information</h2>
            <p class="section-subtitle">Get in touch with your barangay officials for inquiries, concerns, or assistance.</p>

            <div class="row mt-5">
                <div class="col-lg-6 mb-4">
                    <div class="contact-card animate-fade-in-up" style="animation-delay: 0.1s;">
                        <div class="card-body">
                            <h5 class="card-title mb-4">Barangay Contact Details</h5>

                            <div class="contact-info-item">
                                <h5><i class="fas fa-map-marker-alt"></i> Barangay Address</h5>
                                <p><?php echo $barangay_address ? htmlspecialchars($barangay_address) : 'Contact the barangay for address information'; ?></p>
                            </div>

                            <div class="contact-info-item">
                                <h5><i class="fas fa-phone"></i> Contact Numbers</h5>
                                <p><?php echo $contact_number ? htmlspecialchars($contact_number) : 'Contact the barangay for telephone information'; ?></p>
                            </div>

                            <div class="contact-info-item">
                                <h5><i class="fas fa-envelope"></i> Email Address</h5>
                                <p><?php echo $email ? htmlspecialchars($email) : 'Contact the barangay for email information'; ?></p>
                            </div>

                            <div class="contact-info-item">
                                <h5><i class="far fa-clock"></i> Office Hours</h5>
                                <p><?php echo htmlspecialchars($office_hours_weekday); ?><br><?php echo htmlspecialchars($office_hours_weekend); ?></p>
                            </div>

                            <div class="mt-4">

                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="contact-card animate-fade-in-up" style="animation-delay: 0.3s;">
                        <div class="card-body">
                            <h5 class="card-title mb-4">Send us a message</h5>
                            <form>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">Full Name</label>
                                        <input type="text" class="form-control" id="name" placeholder="Enter your full name" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="email" placeholder="Enter your email" required>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="subject" class="form-label">Subject</label>
                                    <input type="text" class="form-control" id="subject" placeholder="Enter message subject" required>
                                </div>

                                <div class="mb-4">
                                    <label for="message" class="form-label">Message</label>
                                    <textarea class="form-control" id="message" rows="5" placeholder="Enter your message here..." required></textarea>
                                </div>

                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-2"></i>Send Message
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <!-- Footer Top Section with Curved Background -->
        <div class="footer-top">
            <div class="container">
                <div class="row">
                    <div class="col-lg-5 col-md-6 mb-5 mb-lg-0">
                        <div class="footer-brand">
                            <div class="footer-logo">
                                <div class="footer-logo-icon">
                                    <i class="fas fa-landmark"></i>
                                </div>
                                <div class="footer-logo-text">
                                    <h4><?php echo $barangay_name; ?></h4>
                                    <p>Barangay Management System</p>
                                </div>
                            </div>
                            <p class="footer-description"><?php echo htmlspecialchars($barangay_description); ?></p>
                            <div class="footer-cta">
                                <?php if (!$is_logged_in): ?>
                                <a href="login.php" class="btn btn-light btn-sm me-2"><i class="fas fa-sign-in-alt me-2"></i>Login</a>
                                <a href="register.php" class="btn btn-outline-light btn-sm"><i class="fas fa-user-plus me-2"></i>Register</a>
                                <?php else: ?>
                                <a href="dashboard.php" class="btn btn-light btn-sm me-2"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                                <a href="request_document.php" class="btn btn-outline-light btn-sm"><i class="fas fa-file-alt me-2"></i>Request Document</a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-6 col-sm-6 mb-4 mb-lg-0">
                        <div class="footer-widget">
                            <h5 class="footer-widget-title">Quick Links</h5>
                            <ul class="footer-links">
                                <li><a href="index.php"><i class="fas fa-angle-right me-2"></i>Home</a></li>
                                <li><a href="#services"><i class="fas fa-angle-right me-2"></i>Services</a></li>
                                <li><a href="#announcements"><i class="fas fa-angle-right me-2"></i>Announcements</a></li>
                                <li><a href="#contact"><i class="fas fa-angle-right me-2"></i>Contact</a></li>
                            </ul>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-6 col-sm-6 mb-4 mb-lg-0">
                        <div class="footer-widget">
                            <h5 class="footer-widget-title">Our Services</h5>
                            <ul class="footer-links">
                                <li><a href="request_document.php?type=clearance"><i class="fas fa-angle-right me-2"></i>Barangay Clearance</a></li>
                                <li><a href="request_document.php?type=certificate"><i class="fas fa-angle-right me-2"></i>Certificates</a></li>
                                <li><a href="request_document.php?type=id"><i class="fas fa-angle-right me-2"></i>Barangay ID</a></li>
                                <li><a href="request_document.php?type=good_standing"><i class="fas fa-angle-right me-2"></i>Good Standing</a></li>
                                <li><a href="request_document.php?type=no_pending_case"><i class="fas fa-angle-right me-2"></i>No Pending Case</a></li>
                            </ul>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6">
                        <div class="footer-widget">
                            <h5 class="footer-widget-title">Contact Us</h5>
                            <div class="footer-contact-info">
                                <div class="footer-contact-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <p><?php echo $barangay_address ? htmlspecialchars($barangay_address) : 'Contact for address'; ?></p>
                                </div>
                                <div class="footer-contact-item">
                                    <i class="fas fa-phone-alt"></i>
                                    <p><?php echo $contact_number ? htmlspecialchars($contact_number) : 'Contact for phone number'; ?></p>
                                </div>
                                <div class="footer-contact-item">
                                    <i class="fas fa-envelope"></i>
                                    <p><?php echo $email ? htmlspecialchars($email) : 'Contact for email'; ?></p>
                                </div>
                                <div class="footer-contact-item">
                                    <i class="fas fa-clock"></i>
                                    <p><?php echo htmlspecialchars(str_replace(['Monday to Friday: ', 'Saturday: '], ['Mon-Fri: ', 'Sat: '], $office_hours_weekday . ', ' . $office_hours_weekend)); ?></p>
                                </div>
                            </div>
                            <div class="footer-social">
                                <?php if (!empty($facebook_url)): ?>
                                <a href="<?php echo htmlspecialchars($facebook_url); ?>" class="footer-social-icon" target="_blank" rel="noopener noreferrer" title="Follow us on Facebook"><i class="fab fa-facebook-f"></i></a>
                                <?php endif; ?>
                                <?php if (!empty($twitter_url)): ?>
                                <a href="<?php echo htmlspecialchars($twitter_url); ?>" class="footer-social-icon" target="_blank" rel="noopener noreferrer" title="Follow us on Twitter"><i class="fab fa-twitter"></i></a>
                                <?php endif; ?>
                                <?php if (!empty($instagram_url)): ?>
                                <a href="<?php echo htmlspecialchars($instagram_url); ?>" class="footer-social-icon" target="_blank" rel="noopener noreferrer" title="Follow us on Instagram"><i class="fab fa-instagram"></i></a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Bottom Section -->
        <div class="footer-bottom">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="copyright">© <?php echo date('Y'); ?> <?php echo $barangay_name; ?> Resident Portal. All rights reserved.</p>
                    </div>
                    <div class="col-md-6">
                        <ul class="footer-bottom-links">
                            <li><a href="#">Privacy Policy</a></li>
                            <li><a href="#">Terms of Use</a></li>
                            <li><a href="#">Disclaimer</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- View Announcement Modal -->
    <div class="modal fade" id="viewAnnouncementModal" tabindex="-1" aria-labelledby="viewAnnouncementModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewAnnouncementModalLabel">
                        <i class="fas fa-bullhorn me-2"></i>
                        Announcement Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="announcement-header mb-4">
                        <div class="announcement-type mb-2" id="announcementType"></div>
                        <h3 class="mb-3" id="announcementTitle"></h3>
                        <div class="announcement-meta">
                            <div class="announcement-meta-item">
                                <i class="far fa-calendar-alt"></i>
                                <span id="announcementDate"></span>
                            </div>
                            <div class="announcement-meta-item" id="startDateContainer" style="display: none;">
                                <i class="fas fa-calendar-day"></i>
                                <span id="announcementStartDate"></span>
                            </div>
                            <div class="announcement-meta-item" id="endDateContainer" style="display: none;">
                                <i class="fas fa-calendar-check"></i>
                                <span id="announcementEndDate"></span>
                            </div>
                        </div>
                    </div>

                    <div class="announcement-venue mb-4" id="venueContainer" style="display: none;">
                        <h5><i class="fas fa-map-marker-alt me-2"></i> Venue</h5>
                        <p id="announcementVenue"></p>
                    </div>

                    <div class="announcement-content mb-4">
                        <h5><i class="fas fa-align-left me-2"></i> Details</h5>
                        <div class="mt-3" id="announcementContent"></div>
                    </div>

                    <div class="announcement-audience mb-4" id="audienceContainer" style="display: none;">
                        <h5><i class="fas fa-users me-2"></i> Target Audience</h5>
                        <p id="announcementAudience"></p>
                    </div>

                    <div class="announcement-image mb-4" id="imageContainer" style="display: none;">
                        <h5><i class="fas fa-image me-2"></i> Attached Image</h5>
                        <div class="mt-3">
                            <img id="announcementImage" class="img-fluid rounded" alt="Announcement Image">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i> Close
                    </button>
                    <?php if ($is_logged_in): ?>
                    <a href="dashboard.php" class="btn btn-primary">
                        <i class="fas fa-tachometer-alt me-2"></i> Go to Dashboard
                    </a>
                    <?php else: ?>
                    <a href="login.php" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt me-2"></i> Login to Access Services
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize particles.js for hero section
            if (document.getElementById('particles-js')) {
                particlesJS('particles-js', {
                    "particles": {
                        "number": {
                            "value": 80,
                            "density": {
                                "enable": true,
                                "value_area": 800
                            }
                        },
                        "color": {
                            "value": "#ffffff"
                        },
                        "shape": {
                            "type": "circle",
                            "stroke": {
                                "width": 0,
                                "color": "#000000"
                            },
                            "polygon": {
                                "nb_sides": 5
                            }
                        },
                        "opacity": {
                            "value": 0.3,
                            "random": true,
                            "anim": {
                                "enable": false,
                                "speed": 1,
                                "opacity_min": 0.1,
                                "sync": false
                            }
                        },
                        "size": {
                            "value": 3,
                            "random": true,
                            "anim": {
                                "enable": false,
                                "speed": 40,
                                "size_min": 0.1,
                                "sync": false
                            }
                        },
                        "line_linked": {
                            "enable": true,
                            "distance": 150,
                            "color": "#ffffff",
                            "opacity": 0.2,
                            "width": 1
                        },
                        "move": {
                            "enable": true,
                            "speed": 2,
                            "direction": "none",
                            "random": false,
                            "straight": false,
                            "out_mode": "out",
                            "bounce": false,
                            "attract": {
                                "enable": false,
                                "rotateX": 600,
                                "rotateY": 1200
                            }
                        }
                    },
                    "interactivity": {
                        "detect_on": "canvas",
                        "events": {
                            "onhover": {
                                "enable": true,
                                "mode": "grab"
                            },
                            "onclick": {
                                "enable": true,
                                "mode": "push"
                            },
                            "resize": true
                        },
                        "modes": {
                            "grab": {
                                "distance": 140,
                                "line_linked": {
                                    "opacity": 0.5
                                }
                            },
                            "bubble": {
                                "distance": 400,
                                "size": 40,
                                "duration": 2,
                                "opacity": 8,
                                "speed": 3
                            },
                            "repulse": {
                                "distance": 200,
                                "duration": 0.4
                            },
                            "push": {
                                "particles_nb": 4
                            },
                            "remove": {
                                "particles_nb": 2
                            }
                        }
                    },
                    "retina_detect": true
                });
            }

            // Service card flip functionality
            $('.service-flip-btn').on('click', function(e) {
                e.preventDefault();
                $(this).closest('.service-card').toggleClass('flipped');
            });

            // Add smooth scrolling to all links
            $("a").on('click', function(event) {
                if (this.hash !== "") {
                    event.preventDefault();
                    var hash = this.hash;
                    $('html, body').animate({
                        scrollTop: $(hash).offset().top - 70
                    }, 800);
                }
            });

            // Animate elements when they come into view
            function animateOnScroll() {
                $('.animate-fade-in-up, .animate-fade-in-down').each(function() {
                    var elementPosition = $(this).offset().top;
                    var topOfWindow = $(window).scrollTop();
                    var windowHeight = $(window).height();

                    if (elementPosition < topOfWindow + windowHeight - 100) {
                        $(this).css('opacity', '1');
                        $(this).css('transform', 'translateY(0)');
                    }
                });
            }

            // Set initial state for animations
            $('.animate-fade-in-up').css({
                'opacity': '0',
                'transform': 'translateY(20px)',
                'transition': 'all 0.5s ease'
            });

            $('.animate-fade-in-down').css({
                'opacity': '0',
                'transform': 'translateY(-20px)',
                'transition': 'all 0.5s ease'
            });

            // Run animation on scroll
            $(window).on('scroll', function() {
                animateOnScroll();
            });

            // Run animation on page load
            animateOnScroll();

            // Add active class to navbar items on scroll
            $(window).scroll(function() {
                var scrollDistance = $(window).scrollTop();

                // Add active class to navbar item when section is in viewport
                $('section').each(function(i) {
                    if ($(this).position().top <= scrollDistance + 100) {
                        $('.navbar-nav a.active').removeClass('active');
                        $('.navbar-nav a').eq(i).addClass('active');
                    }
                });
            }).scroll();

            // Handle announcement modal
            $('#viewAnnouncementModal').on('show.bs.modal', function (event) {
                const button = $(event.relatedTarget);

                // Get data attributes
                const id = button.data('id');
                const title = button.data('title');
                const type = button.data('type');
                const date = button.data('date');
                const startDate = button.data('start-date');
                const endDate = button.data('end-date');
                const venue = button.data('venue');
                const content = button.data('content');
                const audience = button.data('audience');
                const image = button.data('image');

                // Set modal content
                $('#announcementTitle').text(title);

                // Set announcement type with appropriate class
                const typeElement = $('#announcementType');
                typeElement.text(type);
                typeElement.removeClass('general event emergency meeting community');

                // Add the appropriate class based on type
                switch(type.toLowerCase()) {
                    case 'event':
                        typeElement.addClass('event');
                        break;
                    case 'emergency':
                        typeElement.addClass('emergency');
                        break;
                    case 'meeting':
                        typeElement.addClass('meeting');
                        break;
                    case 'community':
                        typeElement.addClass('community');
                        break;
                    default:
                        typeElement.addClass('general');
                }

                // Set dates
                $('#announcementDate').text('Posted: ' + date);

                // Handle optional fields
                if (startDate) {
                    $('#announcementStartDate').text('Starts: ' + startDate);
                    $('#startDateContainer').show();
                } else {
                    $('#startDateContainer').hide();
                }

                if (endDate) {
                    $('#announcementEndDate').text('Ends: ' + endDate);
                    $('#endDateContainer').show();
                } else {
                    $('#endDateContainer').hide();
                }

                if (venue) {
                    $('#announcementVenue').text(venue);
                    $('#venueContainer').show();
                } else {
                    $('#venueContainer').hide();
                }

                // Set content with line breaks
                $('#announcementContent').html(content.replace(/\n/g, '<br>'));

                if (audience) {
                    $('#announcementAudience').text(audience);
                    $('#audienceContainer').show();
                } else {
                    $('#audienceContainer').hide();
                }

                if (image) {
                    $('#announcementImage').attr('src', '../uploads/announcements/' + image);
                    $('#imageContainer').show();
                } else {
                    $('#imageContainer').hide();
                }
            });
        });
    </script>
</body>
</html>